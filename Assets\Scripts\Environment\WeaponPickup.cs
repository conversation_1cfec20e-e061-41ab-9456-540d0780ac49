using UnityEngine;
using System.Collections;

/// <summary>
/// Weapon pickup system for combat training
/// </summary>
public class WeaponPickup : MonoBehaviour
{
    [Header("🔫 Weapon Stats")]
    public string weaponType = "Rifle";
    public float damage = 35f;
    public float range = 15f;
    public float fireRate = 0.3f;
    public int ammo = 30;

    [Header("🎯 Pickup Settings")]
    public float pickupRange = 2f;
    public bool autoPickup = true;
    public float respawnTime = 30f;

    // Visual effects
    private bool isPickedUp = false;
    private Renderer weaponRenderer;
    private Collider weaponCollider;

    void Start()
    {
        weaponRenderer = GetComponent<Renderer>();
        weaponCollider = GetComponent<Collider>();

        // Add floating animation
        StartCoroutine(FloatingAnimation());

        Debug.Log($"🔫 {weaponType} spawned - Damage: {damage}, Range: {range}, Rate: {fireRate}");
    }

    IEnumerator FloatingAnimation()
    {
        Vector3 startPos = transform.position;

        while (!isPickedUp)
        {
            float newY = startPos.y + Mathf.Sin(Time.time * 2f) * 0.2f;
            transform.position = new Vector3(startPos.x, newY, startPos.z);
            transform.Rotate(0, 50f * Time.deltaTime, 0);
            yield return null;
        }
    }

    void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;

        // Check if player or squadmate picked up the weapon
        if (other.CompareTag("Player") || other.GetComponent<SquadMateAgent>() != null)
        {
            PickupWeapon(other.gameObject);
        }
    }

    void PickupWeapon(GameObject picker)
    {
        if (isPickedUp) return;

        isPickedUp = true;

        Debug.Log($"🔫 {picker.name} picked up {weaponType}!");

        // Give weapon to picker
        WeaponSystem weaponSystem = picker.GetComponent<WeaponSystem>();
        if (weaponSystem == null)
        {
            weaponSystem = picker.AddComponent<WeaponSystem>();
        }

        weaponSystem.EquipWeapon(weaponType, damage, range, fireRate, ammo);

        // Reward agent for weapon pickup
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            // Use dynamic reward system if available
            DynamicRewardSystem rewardSystem = agent.GetComponent<DynamicRewardSystem>();
            if (rewardSystem != null)
            {
                // Create a simple weapon loot object for the reward system
                WeaponLoot weaponLoot = new WeaponLoot();
                weaponLoot.itemName = weaponType;
                weaponLoot.damage = damage;
                weaponLoot.range = range;

                // Determine weapon tier based on damage and range
                if (damage >= 70f) // Sniper rifles
                {
                    weaponLoot.weaponTier = WeaponLoot.WeaponTier.Tier3;
                }
                else if (damage >= 40f || weaponType.Contains("M4") || weaponType.Contains("SCAR"))
                {
                    weaponLoot.weaponTier = WeaponLoot.WeaponTier.Tier2;
                }
                else
                {
                    weaponLoot.weaponTier = WeaponLoot.WeaponTier.Tier1;
                }

                // Set weapon class
                if (weaponType.Contains("M4") || weaponType.Contains("SCAR") || weaponType.Contains("AK"))
                {
                    weaponLoot.weaponClass = WeaponLoot.WeaponClass.AssaultRifle;
                }
                else if (weaponType.Contains("UMP") || weaponType.Contains("Vector"))
                {
                    weaponLoot.weaponClass = WeaponLoot.WeaponClass.SMG;
                }
                else if (weaponType.Contains("Kar98") || weaponType.Contains("AWM"))
                {
                    weaponLoot.weaponClass = WeaponLoot.WeaponClass.SniperRifle;
                }

                rewardSystem.RewardWeaponPickup(weaponLoot);
            }
            else
            {
                // Fallback to basic reward
                agent.AddReward(0.2f);
            }

            Debug.Log($"🎯 Agent rewarded for picking up {weaponType}");
        }

        // Visual pickup effect
        StartCoroutine(PickupEffect());
    }

    IEnumerator PickupEffect()
    {
        // Scale up and fade out
        Vector3 originalScale = transform.localScale;
        Color originalColor = weaponRenderer.material.color;

        float timer = 0f;
        while (timer < 0.5f)
        {
            timer += Time.deltaTime * 2f;

            // Scale up
            transform.localScale = Vector3.Lerp(originalScale, originalScale * 1.5f, timer);

            // Fade out
            Color newColor = originalColor;
            newColor.a = 1f - timer;
            weaponRenderer.material.color = newColor;

            yield return null;
        }

        // Hide the weapon
        weaponRenderer.enabled = false;
        weaponCollider.enabled = false;

        // Respawn after delay
        if (respawnTime > 0)
        {
            yield return new WaitForSeconds(respawnTime);
            RespawnWeapon();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void RespawnWeapon()
    {
        isPickedUp = false;
        weaponRenderer.enabled = true;
        weaponCollider.enabled = true;

        // Reset visual properties
        transform.localScale = Vector3.one * 0.5f;
        Color color = weaponRenderer.material.color;
        color.a = 1f;
        weaponRenderer.material.color = color;

        // Respawn effect
        StartCoroutine(RespawnEffect());
        StartCoroutine(FloatingAnimation());

        Debug.Log($"🔄 {weaponType} respawned!");
    }

    IEnumerator RespawnEffect()
    {
        // Spawn with a flash
        for (int i = 0; i < 3; i++)
        {
            weaponRenderer.material.color = Color.white;
            yield return new WaitForSeconds(0.1f);
            weaponRenderer.material.color = Color.yellow;
            yield return new WaitForSeconds(0.1f);
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, pickupRange);
    }
}

/// <summary>
/// Weapon system for entities that can use weapons
/// </summary>
public class WeaponSystem : MonoBehaviour
{
    [Header("🔫 Current Weapon")]
    public string currentWeaponType = "None";
    public float currentDamage = 0f;
    public float currentRange = 0f;
    public float currentFireRate = 0f;
    public int currentAmmo = 0;

    [Header("🎯 Combat Stats")]
    public int totalKills = 0;
    public float totalDamageDealt = 0f;
    public int shotsfired = 0;
    public int shotsHit = 0;

    private float lastShotTime = 0f;
    private SquadMateAgent agent;

    void Start()
    {
        agent = GetComponent<SquadMateAgent>();
    }

    public void EquipWeapon(string weaponType, float damage, float range, float fireRate, int ammo)
    {
        currentWeaponType = weaponType;
        currentDamage = damage;
        currentRange = range;
        currentFireRate = fireRate;
        currentAmmo = ammo;

        // Initialize attachment system if not present
        WeaponAttachmentSystem attachmentSystem = GetComponent<WeaponAttachmentSystem>();
        if (attachmentSystem == null)
        {
            attachmentSystem = gameObject.AddComponent<WeaponAttachmentSystem>();
            attachmentSystem.weaponType = GetWeaponCategory(weaponType);
        }

        Debug.Log($"🔫 {gameObject.name} equipped {weaponType} - {damage} dmg, {range}m range");
    }

    /// <summary>
    /// Apply attachment effects to weapon stats
    /// </summary>
    public void ApplyAttachmentEffects(float recoilReduction, float stabilityBonus,
        float rangeBonus, float damageBonus, int ammoBonus)
    {
        // Apply bonuses to weapon stats
        float modifiedDamage = currentDamage + damageBonus;
        float modifiedRange = currentRange + rangeBonus;
        int modifiedAmmo = currentAmmo + ammoBonus;

        Debug.Log($"🔧 Weapon enhanced by attachments - " +
                 $"Damage: {currentDamage:F0} → {modifiedDamage:F0}, " +
                 $"Range: {currentRange:F0} → {modifiedRange:F0}, " +
                 $"Ammo: {currentAmmo} → {modifiedAmmo}");

        // Store modified stats for combat calculations
        // These would be used in TryShoot method
    }

    /// <summary>
    /// Get weapon category for attachment compatibility
    /// </summary>
    private string GetWeaponCategory(string weaponName)
    {
        weaponName = weaponName.ToUpper();

        if (weaponName.Contains("M416") || weaponName.Contains("SCAR") || weaponName.Contains("AKM"))
            return "AR";
        else if (weaponName.Contains("UMP") || weaponName.Contains("VECTOR"))
            return "SMG";
        else if (weaponName.Contains("KAR98") || weaponName.Contains("AWM"))
            return "Sniper";
        else if (weaponName.Contains("SKS") || weaponName.Contains("MINI14"))
            return "DMR";
        else if (weaponName.Contains("S686") || weaponName.Contains("S12K"))
            return "Shotgun";
        else if (weaponName.Contains("P92") || weaponName.Contains("P1911"))
            return "Pistol";
        else
            return "AR"; // Default to AR
    }

    public bool CanShoot()
    {
        return currentAmmo > 0 && Time.time - lastShotTime >= currentFireRate && currentWeaponType != "None";
    }

    public bool TryShoot(Vector3 targetPosition)
    {
        if (!CanShoot()) return false;

        float distanceToTarget = Vector3.Distance(transform.position, targetPosition);
        if (distanceToTarget > currentRange) return false;

        // Perform shot
        lastShotTime = Time.time;
        currentAmmo--;
        shotsfired++;

        // Check if shot hits (simple raycast)
        Vector3 direction = (targetPosition - transform.position).normalized;
        RaycastHit hit;

        if (Physics.Raycast(transform.position, direction, out hit, currentRange))
        {
            // Check if we hit an enemy
            EnemyAI enemy = hit.collider.GetComponent<EnemyAI>();
            if (enemy != null)
            {
                enemy.TakeDamage(currentDamage);
                shotsHit++;
                totalDamageDealt += currentDamage;

                // Reward agent for successful hit
                if (agent != null)
                {
                    agent.AddReward(0.3f);
                }

                // Check if enemy was killed
                if (enemy.health <= 0)
                {
                    totalKills++;
                    if (agent != null)
                    {
                        agent.AddReward(0.5f); // Extra reward for kill
                    }
                }

                Debug.Log($"🎯 {gameObject.name} hit enemy for {currentDamage} damage!");
                return true;
            }
        }

        Debug.Log($"🔫 {gameObject.name} fired {currentWeaponType} - {currentAmmo} ammo left");
        return false;
    }

    public float GetAccuracy()
    {
        return shotsHit > 0 ? (float)shotsHit / shotsHit : 0f;
    }

    public bool HasWeapon()
    {
        return currentWeaponType != "None" && currentAmmo > 0;
    }

    public bool NeedsAmmo()
    {
        return currentAmmo <= 5; // Low ammo threshold
    }
}
