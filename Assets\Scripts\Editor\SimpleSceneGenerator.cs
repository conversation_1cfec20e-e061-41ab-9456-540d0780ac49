using UnityEngine;
using UnityEditor;
using UnityEngine.AI;
using Unity.MLAgents.Policies;

/// <summary>
/// Simplified scene generator that works with current ML-Agents API
/// Creates a basic training environment without complex API calls
/// </summary>
public class SimpleSceneGenerator : EditorWindow
{
    [MenuItem("SquadMate AI/Generate Simple Training Scene")]
    public static void ShowWindow()
    {
        GetWindow<SimpleSceneGenerator>("Simple Scene Generator");
    }

    private void OnGUI()
    {
        GUILayout.Label("Simple SquadMate Training Scene", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("Creates a basic training environment:");
        GUILayout.Label("• Terrain");
        GUILayout.Label("• Player and SquadMate objects");
        GUILayout.Label("• Basic materials");
        GUILayout.Label("• Spawn points");

        GUILayout.Space(20);

        if (GUILayout.Button("Generate Basic Scene", GUILayout.Height(40)))
        {
            GenerateBasicScene();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("Create Materials", GUILayout.Height(30)))
        {
            CreateBasicMaterials();
        }

        if (GUILayout.Button("Setup Basic Prefabs", GUILayout.Height(30)))
        {
            CreateBasicPrefabs();
        }
    }

    public static void GenerateBasicScene()
    {
        Debug.Log("🚀 Generating Simple Training Scene...");

        // Create basic materials
        CreateBasicMaterials();

        // Create terrain
        CreateBasicTerrain();

        // Create lighting
        CreateBasicLighting();

        // Create player
        CreateBasicPlayer();

        // Create squadmate
        CreateBasicSquadMate();

        // Create environment
        CreateBasicEnvironment();

        // Create spawn points
        CreateBasicSpawnPoints();

        // Create basic prefabs
        CreateBasicPrefabs();

        // Save scene
        SaveBasicScene();

        Debug.Log("✅ Simple Training Scene Generated!");
        Debug.Log("📝 Next steps:");
        Debug.Log("   1. Add SquadMateAgent script to SquadMate");
        Debug.Log("   2. Add PlayerController script to Player");
        Debug.Log("   3. Add GameEnvironment script to Environment");
        Debug.Log("   4. Configure Behavior Parameters on SquadMate");
        Debug.Log("   5. Press Play to start training!");
    }

    private static void CreateBasicMaterials()
    {
        if (!AssetDatabase.IsValidFolder("Assets/Materials"))
            AssetDatabase.CreateFolder("Assets", "Materials");

        // Create basic materials
        CreateMaterial("PlayerMaterial", Color.blue);
        CreateMaterial("SquadMateMaterial", Color.green);
        CreateMaterial("EnemyMaterial", Color.red);
        CreateMaterial("MedkitMaterial", Color.cyan);
        CreateMaterial("WeaponMaterial", Color.yellow);

        AssetDatabase.SaveAssets();
        Debug.Log("✅ Created basic materials");
    }

    private static void CreateMaterial(string name, Color color)
    {
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        mat.color = color;
        AssetDatabase.CreateAsset(mat, $"Assets/Materials/{name}.mat");
    }

    private static void CreateBasicTerrain()
    {
        // Create terrain data
        TerrainData terrainData = new TerrainData();
        terrainData.heightmapResolution = 513;
        terrainData.size = new Vector3(50, 8, 50);

        // Create simple height variation
        float[,] heights = new float[513, 513];
        for (int x = 0; x < 513; x++)
        {
            for (int y = 0; y < 513; y++)
            {
                heights[x, y] = Mathf.PerlinNoise(x * 0.01f, y * 0.01f) * 0.05f;
            }
        }
        terrainData.SetHeights(0, 0, heights);

        // Save terrain data
        if (!AssetDatabase.IsValidFolder("Assets/Terrain"))
            AssetDatabase.CreateFolder("Assets", "Terrain");
        AssetDatabase.CreateAsset(terrainData, "Assets/Terrain/TrainingTerrain.asset");

        // Create terrain object
        GameObject terrainObj = Terrain.CreateTerrainGameObject(terrainData);
        terrainObj.name = "Training Terrain";

        // Mark for navigation
        GameObjectUtility.SetStaticEditorFlags(terrainObj, StaticEditorFlags.NavigationStatic);

        Debug.Log("✅ Created terrain");
    }

    private static void CreateBasicLighting()
    {
        // Create directional light
        GameObject lightObj = new GameObject("Directional Light");
        Light light = lightObj.AddComponent<Light>();
        light.type = LightType.Directional;
        light.intensity = 1.5f;
        lightObj.transform.rotation = Quaternion.Euler(45f, 45f, 0f);

        Debug.Log("✅ Created lighting");
    }

    private static void CreateBasicPlayer()
    {
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        player.transform.localScale = new Vector3(1, 2, 1);

        // Add rigidbody
        Rigidbody rb = player.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Apply material
        Material playerMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PlayerMaterial.mat");
        if (playerMat != null)
            player.GetComponent<Renderer>().material = playerMat;

        Debug.Log("✅ Created Player");
    }

    private static void CreateBasicSquadMate()
    {
        GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        squadmate.name = "SquadMate";
        squadmate.transform.position = new Vector3(3, 1, 0);
        squadmate.transform.localScale = new Vector3(1, 2, 1);

        // Add rigidbody
        Rigidbody rb = squadmate.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Add Behavior Parameters
        BehaviorParameters behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;

        // Apply material
        Material squadmateMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/SquadMateMaterial.mat");
        if (squadmateMat != null)
            squadmate.GetComponent<Renderer>().sharedMaterial = squadmateMat;

        Debug.Log("✅ Created SquadMate with Behavior Parameters");
    }

    private static void CreateBasicEnvironment()
    {
        GameObject environment = new GameObject("Environment");
        environment.transform.position = Vector3.zero;

        Debug.Log("✅ Created Environment");
    }

    private static void CreateBasicSpawnPoints()
    {
        GameObject environment = GameObject.Find("Environment");
        if (environment == null) return;

        Vector3[] positions = new Vector3[]
        {
            new Vector3(10, 1, 10),
            new Vector3(-10, 1, 10),
            new Vector3(10, 1, -10),
            new Vector3(-10, 1, -10),
            new Vector3(15, 1, 0),
            new Vector3(-15, 1, 0),
            new Vector3(0, 1, 15),
            new Vector3(0, 1, -15)
        };

        for (int i = 0; i < positions.Length; i++)
        {
            GameObject spawnPoint = new GameObject($"SpawnPoint_{i + 1}");
            spawnPoint.transform.position = positions[i];
            spawnPoint.transform.SetParent(environment.transform);

            // Add visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            indicator.name = "Indicator";
            indicator.transform.SetParent(spawnPoint.transform);
            indicator.transform.localPosition = Vector3.zero;
            indicator.transform.localScale = Vector3.one * 0.3f;
            indicator.GetComponent<Renderer>().material.color = Color.magenta;
            DestroyImmediate(indicator.GetComponent<Collider>());
        }

        Debug.Log("✅ Created spawn points");
    }

    private static void CreateBasicPrefabs()
    {
        if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
            AssetDatabase.CreateFolder("Assets", "Prefabs");

        // Create Enemy prefab
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "Enemy";
        enemy.transform.localScale = new Vector3(1, 2, 1);

        NavMeshAgent navAgent = enemy.AddComponent<NavMeshAgent>();
        navAgent.speed = 3f;
        navAgent.stoppingDistance = 2f;

        Material enemyMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/EnemyMaterial.mat");
        if (enemyMat != null)
            enemy.GetComponent<Renderer>().material = enemyMat;

        PrefabUtility.SaveAsPrefabAsset(enemy, "Assets/Prefabs/Enemy.prefab");
        DestroyImmediate(enemy);

        // Create Medkit prefab
        GameObject medkit = GameObject.CreatePrimitive(PrimitiveType.Cube);
        medkit.name = "Medkit";
        medkit.transform.localScale = Vector3.one * 0.8f;
        medkit.GetComponent<Collider>().isTrigger = true;

        Material medkitMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/MedkitMaterial.mat");
        if (medkitMat != null)
            medkit.GetComponent<Renderer>().material = medkitMat;

        PrefabUtility.SaveAsPrefabAsset(medkit, "Assets/Prefabs/Medkit.prefab");
        DestroyImmediate(medkit);

        // Create Weapon prefab
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        weapon.name = "Weapon";
        weapon.transform.localScale = new Vector3(0.3f, 1f, 0.3f);
        weapon.GetComponent<Collider>().isTrigger = true;

        Material weaponMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/WeaponMaterial.mat");
        if (weaponMat != null)
            weapon.GetComponent<Renderer>().material = weaponMat;

        PrefabUtility.SaveAsPrefabAsset(weapon, "Assets/Prefabs/Weapon.prefab");
        DestroyImmediate(weapon);

        AssetDatabase.SaveAssets();
        Debug.Log("✅ Created basic prefabs");
    }

    private static void SaveBasicScene()
    {
        if (!AssetDatabase.IsValidFolder("Assets/Scenes"))
            AssetDatabase.CreateFolder("Assets", "Scenes");

        UnityEditor.SceneManagement.EditorSceneManager.SaveScene(
            UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene(),
            "Assets/Scenes/TrainingEnvironment.unity"
        );

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("✅ Scene saved as TrainingEnvironment.unity");
    }
}

/// <summary>
/// Manual setup helper for adding components
/// </summary>
public class ManualSetupHelper
{
    [MenuItem("SquadMate AI/Manual Setup Guide")]
    public static void ShowSetupGuide()
    {
        Debug.Log("📋 Manual Setup Guide:");
        Debug.Log("==================================================");
        Debug.Log("1. Select SquadMate GameObject");
        Debug.Log("   • Add Component: SquadMateAgent");
        Debug.Log("   • Add Component: RewardCalculator");
        Debug.Log("   • Add Component: SquadMateDecisionTree");
        Debug.Log("");
        Debug.Log("2. Select Player GameObject");
        Debug.Log("   • Add Component: PlayerController");
        Debug.Log("");
        Debug.Log("3. Select Environment GameObject");
        Debug.Log("   • Add Component: GameEnvironment");
        Debug.Log("   • Add Component: ObjectSpawner");
        Debug.Log("");
        Debug.Log("4. Configure Behavior Parameters on SquadMate:");
        Debug.Log("   • Behavior Name: SquadMate");
        Debug.Log("   • Behavior Type: Default");
        Debug.Log("");
        Debug.Log("5. Assign references in GameEnvironment:");
        Debug.Log("   • Player: Drag Player GameObject");
        Debug.Log("   • SquadMate: Drag SquadMate GameObject");
        Debug.Log("   • Prefabs: Assign Enemy, Medkit, Weapon prefabs");
        Debug.Log("   • Spawn Points: Assign SpawnPoint GameObjects");
        Debug.Log("");
        Debug.Log("6. Bake NavMesh:");
        Debug.Log("   • Window → AI → Navigation");
        Debug.Log("   • Select terrain → Navigation Static");
        Debug.Log("   • Bake tab → Bake");
        Debug.Log("");
        Debug.Log("7. Press Play to start training!");
    }
}
