# 🚀 SquadMate AI - One Click Training Guide

## 🎯 **Three Ways to Start Training (Choose One)**

### 🥇 **Method 1: Unity Editor (Recommended)**
1. Open Unity
2. Go to menu: `SquadMate AI → 🚀 INSTANT START TRAINING`
3. Click "YES - START NOW!"
4. Press PLAY when prompted
5. ✅ **Done!** Training starts automatically

### 🥈 **Method 2: Unity Window**
1. Open Unity
2. Go to menu: `SquadMate AI → 🚀 ONE CLICK TRAINING`
3. Click the big green "🚀 START ONE CLICK TRAINING NOW!" button
4. Press PLAY when prompted
5. ✅ **Done!** Training starts automatically

### 🥉 **Method 3: Double-Click Batch File**
1. Double-click `ONE_CLICK_TRAINING.bat` in your project folder
2. Wait for setup to complete
3. Open Unity when prompted
4. Go to: `SquadMate AI → 🚀 ONE CLICK TRAINING`
5. Press PLAY when prompted
6. ✅ **Done!** Training starts automatically

---

## 🎮 **What Happens Automatically**

### ⚡ **Instant Setup (< 30 seconds)**
- ✅ Creates `PUBGTrainingArena.unity` scene
- ✅ Spawns SquadMate agent with all components
- ✅ Creates player character
- ✅ Spawns 3 enemy bots
- ✅ Places test loot items (weapons + healing)
- ✅ Configures training environment
- ✅ Generates ML-Agents config file
- ✅ Starts TensorBoard monitoring
- ✅ Launches ML-Agents training

### 🎯 **Training Features**
- ✅ **Immediate Rewards**: Agent gets +0.5 for weapons, +0.3 for healing
- ✅ **Auto Episodes**: 2-minute episodes with auto-reset
- ✅ **Debug Logging**: Real-time reward tracking in Console
- ✅ **Visual Feedback**: Colored loot items (blue=weapons, green=healing)
- ✅ **Progress Monitoring**: TensorBoard opens automatically

---

## 📊 **Monitoring Training Progress**

### 🖥️ **Unity Console**
Look for these messages:
```
🎯 Setting up Simple Training Environment...
🔄 Episode 1 started
🎯 Agent picked up TestWeapon_1 - Reward: +0.5
🔫 TeammateBot now has a weapon!
📊 Episode 1 - Time: 15.2s, Reward: 1.23
```

### 📈 **TensorBoard (Auto-Opens)**
- **URL**: http://localhost:6006
- **Key Metrics**:
  - `Environment/Cumulative Reward` - Should increase over time
  - `Environment/Episode Length` - Should stabilize around 120s
  - `Losses/Policy Loss` - Should decrease over time

### 🎯 **Expected Progress**
- **0-1k steps**: Random movement, occasional rewards
- **1k-10k steps**: More directed movement toward loot
- **10k-50k steps**: Consistent loot collection
- **50k+ steps**: Advanced tactical behavior

---

## 🛠️ **Troubleshooting**

### ❌ **"Training not starting"**
**Solution**: 
1. Check Unity Console for error messages
2. Ensure Python and ML-Agents are installed
3. Try: `SquadMate AI → 🔄 Reset Everything`

### ❌ **"Zero rewards showing"**
**Solution**:
1. Verify agent is moving (check Scene view)
2. Look for colored cubes (loot items) in scene
3. Check Console for pickup messages
4. Try manual test: Select Training Arena → "Give Test Reward"

### ❌ **"TensorBoard not opening"**
**Solution**:
1. Manually open: http://localhost:6006
2. Check if `tensorboard` command works in terminal
3. Install: `pip install tensorboard`

### ❌ **"Episodes not restarting"**
**Solution**:
1. Check `SimpleTrainingEnvironment` component exists
2. Verify `autoReset = true` in inspector
3. Look for "Episode X started" messages in Console

---

## 🎯 **Training Configuration**

### 📝 **Auto-Generated Config**
```yaml
behaviors:
  SquadMate:
    trainer_type: ppo
    max_steps: 500000        # 500k steps (~2-4 hours)
    time_horizon: 64         # 64 steps per decision
    summary_freq: 1000       # Log every 1000 steps
    batch_size: 1024         # Large batch for stability
    learning_rate: 3.0e-4    # Conservative learning rate
```

### 🎮 **Agent Actions**
- **Continuous**: Move X, Move Z, Rotate (3 actions)
- **Discrete**: Do Nothing, Revive, Attack, Pickup, Heal, Cover, Grenade, Reload (8 actions)

### 🏆 **Reward System**
- **Survival**: +0.01 per frame
- **Formation**: +0.05 for good distance to player
- **Loot Pickup**: +0.3 to +0.5 based on item type
- **Combat**: +0.1 for hits, +0.3 for kills
- **Death**: -1.0 penalty

---

## 🎉 **Success Indicators**

### ✅ **Training is Working When You See:**
1. **Unity Console**: Regular reward messages
2. **TensorBoard**: Increasing cumulative reward graph
3. **Scene View**: Agent moving toward loot items
4. **Episode Resets**: "Episode X started" every 2 minutes

### 🏆 **Training is Successful When:**
1. **Consistent Loot Collection**: Agent reliably finds and picks up items
2. **Tactical Movement**: Agent moves strategically around enemies
3. **Formation Keeping**: Agent maintains good distance from player
4. **Combat Engagement**: Agent attacks enemies when armed

---

## 🚀 **Next Steps After Training**

### 📦 **Export Trained Model**
1. Training automatically saves to `results/` folder
2. Copy `SquadMate.onnx` to `Assets/Models/`
3. Assign model in agent's Behavior Parameters
4. Set Behavior Type to "Inference Only"
5. Test in play mode!

### 🎮 **Test Your AI**
1. Create test scene with player, enemies, loot
2. Place trained SquadMate agent
3. Press Play and watch your AI in action!
4. Fine-tune by adjusting reward values and retraining

---

## 💡 **Pro Tips**

### 🎯 **Faster Training**
- Use GPU: Check "Use GPU" in training window
- Increase Time Scale: Set to 3x-5x in Unity
- Multiple Agents: Add more SquadMate agents to scene

### 🔧 **Better Results**
- Longer Training: Increase max_steps to 1M+
- Curriculum Learning: Start simple, add complexity
- Reward Tuning: Adjust reward values based on behavior

### 📊 **Monitoring**
- Keep TensorBoard open during training
- Check Unity Console regularly
- Save good models at different training stages

---

🎮 **Happy Training! Your PUBG-style AI squadmate awaits!** 🤖
