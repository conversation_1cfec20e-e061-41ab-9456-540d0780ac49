using UnityEngine;

/// <summary>
/// Simple weapon pickup script that matches the interface you showed
/// Works with both InventorySystem and existing WeaponSystem
/// </summary>
public class SimpleWeaponPickup : MonoBehaviour
{
    [Header("🔫 Weapon Settings")]
    public string weaponName = "M416";
    public float damage = 43f;
    public float range = 25f;
    public float fireRate = 0.086f;
    public int ammo = 30;
    
    [Header("🎨 Visual Settings")]
    public bool enableFloating = true;
    public float floatSpeed = 2f;
    public float floatHeight = 0.2f;
    public float rotationSpeed = 50f;
    
    private Vector3 startPosition;
    private bool isPickedUp = false;
    
    void Start()
    {
        startPosition = transform.position;
        
        // Ensure collider is trigger
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.isTrigger = true;
        }
        
        Debug.Log($"🔫 {weaponName} pickup ready - Damage: {damage}, Range: {range}");
    }
    
    void Update()
    {
        if (isPickedUp) return;
        
        // Floating animation
        if (enableFloating)
        {
            float newY = startPosition.y + Mathf.Sin(Time.time * floatSpeed) * floatHeight;
            transform.position = new Vector3(startPosition.x, newY, startPosition.z);
            transform.Rotate(0, rotationSpeed * Time.deltaTime, 0);
        }
    }
    
    private void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;
        
        if (other.CompareTag("Player") || other.CompareTag("Agent"))
        {
            var inventory = other.GetComponent<InventorySystem>();
            if (inventory != null)
            {
                bool success = inventory.AddWeapon(weaponName);
                if (success)
                {
                    // Also try to equip the weapon using existing WeaponSystem
                    WeaponSystem weaponSystem = other.GetComponent<WeaponSystem>();
                    if (weaponSystem == null)
                    {
                        weaponSystem = other.gameObject.AddComponent<WeaponSystem>();
                    }
                    weaponSystem.EquipWeapon(weaponName, damage, range, fireRate, ammo);
                    
                    // Reward agent if applicable
                    SquadMateAgent agent = other.GetComponent<SquadMateAgent>();
                    if (agent != null)
                    {
                        float reward = CalculateWeaponReward();
                        agent.AddReward(reward);
                        Debug.Log($"🎯 Agent rewarded {reward:F2} for picking up {weaponName}");
                    }
                    
                    PickupWeapon(other.gameObject);
                }
                else
                {
                    Debug.Log($"❌ {other.name} cannot pickup {weaponName} - inventory full");
                }
            }
            else
            {
                Debug.LogWarning($"⚠️ {other.name} has no InventorySystem component");
            }
        }
    }
    
    private float CalculateWeaponReward()
    {
        float baseReward = 0.2f;
        
        // Higher reward for better weapons
        if (weaponName.Contains("M416") || weaponName.Contains("SCAR"))
        {
            baseReward = 0.4f; // High-tier AR
        }
        else if (weaponName.Contains("Kar98") || weaponName.Contains("AWM"))
        {
            baseReward = 0.5f; // Sniper rifles
        }
        else if (weaponName.Contains("UMP") || weaponName.Contains("Vector"))
        {
            baseReward = 0.3f; // SMGs
        }
        
        // Bonus for high damage weapons
        if (damage > 50f)
        {
            baseReward += 0.1f;
        }
        
        return baseReward;
    }
    
    private void PickupWeapon(GameObject picker)
    {
        isPickedUp = true;
        
        Debug.Log($"🔫 {picker.name} picked up {weaponName}!");
        
        // Visual pickup effect
        StartCoroutine(PickupEffect());
    }
    
    private System.Collections.IEnumerator PickupEffect()
    {
        // Scale up and fade out effect
        Vector3 originalScale = transform.localScale;
        Renderer renderer = GetComponent<Renderer>();
        Color originalColor = Color.white;
        
        if (renderer != null && renderer.material != null)
        {
            originalColor = renderer.material.color;
        }
        
        float timer = 0f;
        float duration = 0.5f;
        
        while (timer < duration)
        {
            timer += Time.deltaTime;
            float progress = timer / duration;
            
            // Scale up
            transform.localScale = Vector3.Lerp(originalScale, originalScale * 1.5f, progress);
            
            // Fade out
            if (renderer != null && renderer.material != null)
            {
                Color newColor = originalColor;
                newColor.a = 1f - progress;
                renderer.material.color = newColor;
            }
            
            yield return null;
        }
        
        // Destroy the pickup
        Destroy(gameObject);
    }
    
    /// <summary>
    /// Set weapon properties programmatically
    /// </summary>
    public void SetWeaponProperties(string name, float dmg, float rng, float rate, int ammunition)
    {
        weaponName = name;
        damage = dmg;
        range = rng;
        fireRate = rate;
        ammo = ammunition;
    }
    
    /// <summary>
    /// Create a weapon pickup from weapon data
    /// </summary>
    public static GameObject CreateWeaponPickup(string weaponName, Vector3 position, Transform parent = null)
    {
        GameObject pickup = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        pickup.name = $"{weaponName}_Pickup";
        pickup.transform.position = position;
        pickup.transform.localScale = new Vector3(0.3f, 1f, 0.3f);
        
        if (parent != null)
        {
            pickup.transform.SetParent(parent);
        }
        
        // Add the pickup script
        SimpleWeaponPickup weaponPickup = pickup.AddComponent<SimpleWeaponPickup>();
        weaponPickup.weaponName = weaponName;
        
        // Set weapon-specific properties
        switch (weaponName.ToLower())
        {
            case "m416":
                weaponPickup.SetWeaponProperties("M416", 43f, 25f, 0.086f, 30);
                pickup.GetComponent<Renderer>().material.color = Color.blue;
                break;
            case "ump45":
                weaponPickup.SetWeaponProperties("UMP45", 35f, 15f, 0.092f, 25);
                pickup.GetComponent<Renderer>().material.color = Color.cyan;
                break;
            case "kar98k":
                weaponPickup.SetWeaponProperties("Kar98k", 75f, 50f, 1.8f, 5);
                pickup.GetComponent<Renderer>().material.color = Color.magenta;
                break;
            default:
                weaponPickup.SetWeaponProperties(weaponName, 30f, 20f, 0.1f, 20);
                pickup.GetComponent<Renderer>().material.color = Color.yellow;
                break;
        }
        
        // Ensure collider is trigger
        pickup.GetComponent<Collider>().isTrigger = true;
        
        // Add tag
        pickup.tag = "Weapon";
        
        Debug.Log($"✅ Created {weaponName} pickup at {position}");
        return pickup;
    }
}
