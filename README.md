# SquadMate AI - PUBG-Style Tactical AI Agent

A Unity ML-Agents project that trains an AI teammate for PUBG-style tactical combat scenarios. The AI learns to make intelligent decisions about combat, loot prioritization, healing, and team support.

## 🎯 Project Structure

```text
SquadMateAI/
├── Assets/
│   ├── Agents/
│   │   └── SquadMateAgent.cs          # Main ML-Agents agent
│   ├── Prefabs/
│   │   ├── TeammateBot.prefab         # AI teammate prefab
│   │   ├── EnemyBot.prefab           # Enemy AI prefab
│   │   ├── Medkit.prefab             # Healing item prefab
│   │   ├── M416.prefab               # Assault rifle prefab
│   │   └── UMP45.prefab              # SMG prefab
│   ├── Scenes/
│   │   └── PUBGTrainingArena.unity   # Training environment
│   ├── Scripts/
│   │   ├── Gun.cs                    # Weapon system
│   │   ├── WeaponPickup.cs           # Weapon pickup logic
│   │   ├── MedkitPickup.cs           # Healing pickup logic
│   │   ├── HealthSystem.cs           # Health management
│   │   ├── ReviveSystem.cs           # Player revival system
│   │   ├── LootZoneManager.cs        # Dynamic loot spawning
│   │   ├── DynamicRewardSystem.cs    # Contextual AI rewards
│   │   └── InventorySystem.cs        # Simple inventory interface
│   └── Models/
│       └── squadmate_policy.onnx     # Trained AI model (after training)
├── config/
│   └── squadmate-ppo.yaml           # ML-Agents training configuration
├── ai_decision_tree.json            # AI behavior decision tree
└── README.md                        # This file
```

## 🚀 Quick Start

### 1. Setup Unity Scene

```csharp
// In Unity Editor
SquadMate AI → 🎯 Create Simple PUBG Scene
```

### 2. Create Prefabs

```csharp
// In Unity Editor
SquadMate AI → 📦 Create Simple Prefabs
```

### 3. Start Training

```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=squadmate_v1
```

## 🎮 Features

### 🤖 AI Behaviors

- **Combat**: Engages enemies with tactical positioning
- **Loot Management**: Prioritizes high-tier weapons and healing items
- **Team Support**: Revives downed players and provides cover
- **Survival**: Uses healing items strategically and seeks cover when damaged

### 🔫 Weapon System

- **M416**: High-tier assault rifle (43 damage, 25m range)
- **UMP45**: Reliable SMG (35 damage, 15m range)
- **Kar98k**: Powerful sniper rifle (75 damage, 50m range)

### 💊 Healing System

- **Med Kit**: Full heal (100 HP, 8s use time)
- **First Aid Kit**: Major heal (75 HP, 6s use time)
- **Bandage**: Minor heal (10 HP, 4s use time)
- **Energy Drink**: Instant boost (40 HP, instant)

### 🎯 Reward System

- **Weapon Pickup**: 0.2-0.5 based on weapon tier
- **Healing Pickup**: 0.2-0.5 based on health status
- **Combat Success**: 0.3 for hits, 0.5 for kills
- **Team Support**: 0.5 for revives, 0.1 for cover

## 🧠 AI Decision Tree

The AI uses a hierarchical decision tree with the following priority:

1. **Emergency Response** (Highest Priority)
   - Find cover when critically damaged
   - Heal when safe and low health
   - Revive downed teammates

2. **Combat Behavior**
   - Equip best available weapon
   - Engage enemies in range
   - Use tactical positioning and grenades

3. **Loot Behavior**
   - Prioritize weapon upgrades
   - Collect healing items based on health
   - Gather armor and utility items

4. **Support Behavior** (Lowest Priority)
   - Follow player at appropriate distance
   - Watch flanks and provide overwatch
   - Share loot when player needs items

## 📊 Training Configuration

### Hyperparameters

- **Algorithm**: PPO (Proximal Policy Optimization)
- **Learning Rate**: 3.0e-4
- **Batch Size**: 1024
- **Buffer Size**: 10240
- **Max Steps**: 2,000,000

### Network Architecture

- **Hidden Units**: 128
- **Layers**: 2
- **Normalization**: Disabled
- **Time Horizon**: 64

## 🎯 Usage Examples

### Simple Weapon Pickup

```csharp
// Scripts/WeaponPickup.cs
public class WeaponPickup : MonoBehaviour
{
    public string weaponName = "M416";

    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") || other.CompareTag("Agent"))
        {
            var inventory = other.GetComponent<InventorySystem>();
            if (inventory != null)
            {
                inventory.AddWeapon(weaponName);
                Destroy(gameObject);
            }
        }
    }
}
```

### Simple Medkit Pickup

```csharp
// Scripts/MedkitPickup.cs
public class MedkitPickup : MonoBehaviour
{
    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") || other.CompareTag("Agent"))
        {
            var inventory = other.GetComponent<InventorySystem>();
            if (inventory != null)
            {
                inventory.AddHealing("Medkit");
                Destroy(gameObject);
            }
        }
    }
}
```

## 📈 Training Progress

Expected training milestones:

- **100k steps**: Basic movement and loot collection
- **500k steps**: Combat engagement and weapon usage
- **1M steps**: Tactical positioning and team support
- **2M steps**: Advanced decision-making and optimization

---

🎮 **Ready to train your ultimate PUBG squadmate!** 🤖
