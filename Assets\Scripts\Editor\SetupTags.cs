using UnityEngine;
using UnityEditor;

/// <summary>
/// Editor script to set up required tags for the training environment
/// </summary>
public class SetupTags : EditorWindow
{
    [MenuItem("SquadMate/Setup Tags")]
    static void ShowWindow()
    {
        SetupRequiredTags();
    }

    static void SetupRequiredTags()
    {
        // Required tags for the training environment
        string[] requiredTags = {
            "Player",
            "SquadMate", 
            "Enemy",
            "Medkit",
            "Weapon",
            "Cover",
            "SpawnPoint"
        };

        foreach (string tag in requiredTags)
        {
            AddTag(tag);
        }

        Debug.Log("✅ All required tags have been set up!");
    }

    static void AddTag(string tag)
    {
        // Get the tag manager
        SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
        SerializedProperty tagsProp = tagManager.FindProperty("tags");

        // Check if tag already exists
        bool found = false;
        for (int i = 0; i < tagsProp.arraySize; i++)
        {
            SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
            if (t.stringValue.Equals(tag))
            {
                found = true;
                break;
            }
        }

        // Add tag if it doesn't exist
        if (!found)
        {
            tagsProp.InsertArrayElementAtIndex(0);
            SerializedProperty newTagProp = tagsProp.GetArrayElementAtIndex(0);
            newTagProp.stringValue = tag;
            tagManager.ApplyModifiedProperties();
            Debug.Log($"✅ Added tag: {tag}");
        }
        else
        {
            Debug.Log($"📋 Tag already exists: {tag}");
        }
    }
}
