using UnityEngine;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Manages loot spawning in defined zones for PUBG-style training
/// </summary>
public class LootZoneManager : MonoBehaviour
{
    [Header("🎒 Loot Zone Settings")]
    public LootZone[] lootZones;
    public float spawnInterval = 30f;
    public int maxLootItems = 50;
    public bool enableDynamicSpawning = true;
    
    [Header("📦 Loot Prefabs")]
    public GameObject[] weaponPrefabs;
    public GameObject[] healingPrefabs;
    public GameObject[] attachmentPrefabs;
    public GameObject[] armorPrefabs;
    public GameObject[] utilityPrefabs;
    
    [Header("🎯 Spawn Probabilities")]
    [Range(0f, 1f)] public float weaponSpawnChance = 0.3f;
    [Range(0f, 1f)] public float healingSpawnChance = 0.4f;
    [Range(0f, 1f)] public float attachmentSpawnChance = 0.2f;
    [Range(0f, 1f)] public float armorSpawnChance = 0.15f;
    [Range(0f, 1f)] public float utilitySpawnChance = 0.1f;

    private List<GameObject> activeLootItems = new List<GameObject>();
    private SquadMateAgent squadMateAgent;
    private float lastSpawnTime;

    [System.Serializable]
    public class LootZone
    {
        public string zoneName;
        public Transform zoneCenter;
        public float zoneRadius = 10f;
        public LootZoneType zoneType = LootZoneType.Mixed;
        public int maxItemsInZone = 8;
        public float spawnHeightOffset = 0.5f;
        
        [Header("Zone-Specific Spawn Rates")]
        public float weaponMultiplier = 1f;
        public float healingMultiplier = 1f;
        public float attachmentMultiplier = 1f;
        public float armorMultiplier = 1f;
        public float utilityMultiplier = 1f;
    }

    public enum LootZoneType
    {
        Mixed,          // All types of loot
        Military,       // More weapons and attachments
        Medical,        // More healing items
        Residential,    // Basic loot
        Industrial,     // More utilities and armor
        HighTier        // Rare/epic items only
    }

    void Start()
    {
        squadMateAgent = FindObjectOfType<SquadMateAgent>();
        
        if (enableDynamicSpawning)
        {
            StartCoroutine(DynamicLootSpawning());
        }
        
        // Initial loot spawn
        SpawnInitialLoot();
    }

    void SpawnInitialLoot()
    {
        foreach (LootZone zone in lootZones)
        {
            SpawnLootInZone(zone, zone.maxItemsInZone / 2); // Start with half capacity
        }
    }

    IEnumerator DynamicLootSpawning()
    {
        while (enableDynamicSpawning)
        {
            yield return new WaitForSeconds(spawnInterval);
            
            if (activeLootItems.Count < maxLootItems)
            {
                // Clean up destroyed items
                CleanupDestroyedLoot();
                
                // Spawn new loot in zones that need it
                foreach (LootZone zone in lootZones)
                {
                    int itemsInZone = GetItemsInZone(zone);
                    if (itemsInZone < zone.maxItemsInZone)
                    {
                        SpawnLootInZone(zone, 1);
                    }
                }
            }
        }
    }

    void SpawnLootInZone(LootZone zone, int itemCount)
    {
        for (int i = 0; i < itemCount; i++)
        {
            Vector3 spawnPosition = GetRandomPositionInZone(zone);
            GameObject lootPrefab = SelectLootPrefab(zone);
            
            if (lootPrefab != null)
            {
                GameObject lootItem = Instantiate(lootPrefab, spawnPosition, Quaternion.identity);
                activeLootItems.Add(lootItem);
                
                // Notify SquadMate of new loot
                NotifySquadMateOfLoot(lootItem);
                
                Debug.Log($"📦 Spawned {lootItem.name} in {zone.zoneName}");
            }
        }
    }

    Vector3 GetRandomPositionInZone(LootZone zone)
    {
        Vector2 randomCircle = Random.insideUnitCircle * zone.zoneRadius;
        Vector3 spawnPos = zone.zoneCenter.position + new Vector3(randomCircle.x, zone.spawnHeightOffset, randomCircle.y);
        
        // Raycast to ground
        RaycastHit hit;
        if (Physics.Raycast(spawnPos + Vector3.up * 10f, Vector3.down, out hit, 20f))
        {
            spawnPos.y = hit.point.y + zone.spawnHeightOffset;
        }
        
        return spawnPos;
    }

    GameObject SelectLootPrefab(LootZone zone)
    {
        // Calculate spawn chances based on zone type
        float weaponChance = weaponSpawnChance * zone.weaponMultiplier;
        float healingChance = healingSpawnChance * zone.healingMultiplier;
        float attachmentChance = attachmentSpawnChance * zone.attachmentMultiplier;
        float armorChance = armorSpawnChance * zone.armorMultiplier;
        float utilityChance = utilitySpawnChance * zone.utilityMultiplier;
        
        // Adjust chances based on zone type
        switch (zone.zoneType)
        {
            case LootZoneType.Military:
                weaponChance *= 2f;
                attachmentChance *= 1.5f;
                break;
            case LootZoneType.Medical:
                healingChance *= 3f;
                break;
            case LootZoneType.Residential:
                weaponChance *= 0.5f;
                healingChance *= 1.2f;
                break;
            case LootZoneType.Industrial:
                armorChance *= 2f;
                utilityChance *= 1.5f;
                break;
            case LootZoneType.HighTier:
                // Only spawn rare+ items
                break;
        }
        
        float totalChance = weaponChance + healingChance + attachmentChance + armorChance + utilityChance;
        float randomValue = Random.Range(0f, totalChance);
        
        // Select loot type
        if (randomValue < weaponChance && weaponPrefabs.Length > 0)
        {
            return weaponPrefabs[Random.Range(0, weaponPrefabs.Length)];
        }
        randomValue -= weaponChance;
        
        if (randomValue < healingChance && healingPrefabs.Length > 0)
        {
            return healingPrefabs[Random.Range(0, healingPrefabs.Length)];
        }
        randomValue -= healingChance;
        
        if (randomValue < attachmentChance && attachmentPrefabs.Length > 0)
        {
            return attachmentPrefabs[Random.Range(0, attachmentPrefabs.Length)];
        }
        randomValue -= attachmentChance;
        
        if (randomValue < armorChance && armorPrefabs.Length > 0)
        {
            return armorPrefabs[Random.Range(0, armorPrefabs.Length)];
        }
        
        if (utilityPrefabs.Length > 0)
        {
            return utilityPrefabs[Random.Range(0, utilityPrefabs.Length)];
        }
        
        return null;
    }

    int GetItemsInZone(LootZone zone)
    {
        int count = 0;
        foreach (GameObject item in activeLootItems)
        {
            if (item != null)
            {
                float distance = Vector3.Distance(item.transform.position, zone.zoneCenter.position);
                if (distance <= zone.zoneRadius)
                {
                    count++;
                }
            }
        }
        return count;
    }

    void CleanupDestroyedLoot()
    {
        activeLootItems.RemoveAll(item => item == null);
    }

    void NotifySquadMateOfLoot(GameObject lootItem)
    {
        if (squadMateAgent != null)
        {
            // Add to agent's awareness (implement in SquadMateAgent as needed)
            LootItem loot = lootItem.GetComponent<LootItem>();
            if (loot != null)
            {
                float priority = loot.GetAIPriority(squadMateAgent);
                Debug.Log($"🎯 Notified SquadMate of {loot.itemName} (Priority: {priority:F2})");
            }
        }
    }

    public void RemoveLootItem(GameObject lootItem)
    {
        activeLootItems.Remove(lootItem);
    }

    public List<GameObject> GetNearbyLoot(Vector3 position, float radius)
    {
        List<GameObject> nearbyLoot = new List<GameObject>();
        
        foreach (GameObject item in activeLootItems)
        {
            if (item != null)
            {
                float distance = Vector3.Distance(item.transform.position, position);
                if (distance <= radius)
                {
                    nearbyLoot.Add(item);
                }
            }
        }
        
        return nearbyLoot;
    }

    public GameObject GetBestLootForAgent(Vector3 agentPosition, SquadMateAgent agent)
    {
        GameObject bestLoot = null;
        float bestPriority = 0f;
        float maxSearchRadius = 20f;
        
        foreach (GameObject item in activeLootItems)
        {
            if (item != null)
            {
                float distance = Vector3.Distance(item.transform.position, agentPosition);
                if (distance <= maxSearchRadius)
                {
                    LootItem loot = item.GetComponent<LootItem>();
                    if (loot != null)
                    {
                        float priority = loot.GetAIPriority(agent);
                        // Reduce priority based on distance
                        priority *= (1f - (distance / maxSearchRadius));
                        
                        if (priority > bestPriority)
                        {
                            bestPriority = priority;
                            bestLoot = item;
                        }
                    }
                }
            }
        }
        
        return bestLoot;
    }

    void OnDrawGizmosSelected()
    {
        if (lootZones != null)
        {
            foreach (LootZone zone in lootZones)
            {
                if (zone.zoneCenter != null)
                {
                    // Draw zone boundaries
                    Gizmos.color = GetZoneColor(zone.zoneType);
                    Gizmos.DrawWireSphere(zone.zoneCenter.position, zone.zoneRadius);
                    
                    // Draw zone label
                    Gizmos.color = Color.white;
                    Vector3 labelPos = zone.zoneCenter.position + Vector3.up * 2f;
                    #if UNITY_EDITOR
                    UnityEditor.Handles.Label(labelPos, zone.zoneName);
                    #endif
                }
            }
        }
    }

    Color GetZoneColor(LootZoneType zoneType)
    {
        switch (zoneType)
        {
            case LootZoneType.Military: return Color.red;
            case LootZoneType.Medical: return Color.green;
            case LootZoneType.Residential: return Color.blue;
            case LootZoneType.Industrial: return Color.yellow;
            case LootZoneType.HighTier: return Color.magenta;
            default: return Color.white;
        }
    }
}
