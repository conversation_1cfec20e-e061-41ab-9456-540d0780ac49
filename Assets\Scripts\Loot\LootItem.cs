using UnityEngine;

/// <summary>
/// Base class for all loot items in the PUBG-style training environment
/// </summary>
public abstract class LootItem : MonoBehaviour
{
    [Header("🎒 Loot Item Base")]
    public string itemName;
    public LootType lootType;
    public LootRarity rarity = LootRarity.Common;
    public float rewardValue = 0.1f;
    public bool canStack = false;
    public int maxStackSize = 1;
    public Sprite itemIcon;

    [Header("📊 Item Stats")]
    public float itemValue = 1f; // For AI priority calculation
    public string description;

    [Header("🎨 Visual Settings")]
    public float rotationSpeed = 30f;
    public float bobSpeed = 2f;
    public float bobHeight = 0.3f;
    public Color itemColor = Color.white;

    protected Vector3 startPosition;
    protected bool isPickedUp = false;
    protected Renderer itemRenderer;
    protected Collider itemCollider;

    public enum LootType
    {
        Weapon,
        Healing,
        Attachment,
        Armor,
        Utility,
        Ammo,
        Backpack
    }

    public enum LootRarity
    {
        Common,    // White
        Uncommon,  // Green  
        Rare,      // Blue
        Epic,      // Purple
        Legendary  // Gold
    }

    protected virtual void Start()
    {
        startPosition = transform.position;
        itemRenderer = GetComponent<Renderer>();
        itemCollider = GetComponent<Collider>();

        // Ensure trigger collider
        if (itemCollider != null)
        {
            itemCollider.isTrigger = true;
        }

        // Set rarity color
        SetRarityColor();

        // Add LootItem tag
        gameObject.tag = "LootItem";
    }

    protected virtual void Update()
    {
        if (isPickedUp) return;

        // Rotate item
        transform.Rotate(Vector3.up * rotationSpeed * Time.deltaTime);

        // Bob up and down
        float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
        transform.position = new Vector3(transform.position.x, newY, transform.position.z);
    }

    protected virtual void SetRarityColor()
    {
        if (itemRenderer == null) return;

        Color rarityColor = GetRarityColor();

        // Create material if needed
        if (itemRenderer.material == null)
        {
            itemRenderer.material = new Material(Shader.Find("Standard"));
        }

        itemRenderer.material.color = rarityColor;
    }

    protected Color GetRarityColor()
    {
        switch (rarity)
        {
            case LootRarity.Common: return Color.white;
            case LootRarity.Uncommon: return Color.green;
            case LootRarity.Rare: return Color.blue;
            case LootRarity.Epic: return Color.magenta;
            case LootRarity.Legendary: return Color.yellow;
            default: return itemColor;
        }
    }

    protected virtual void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;

        // Check if player or squadmate picked it up
        if (other.CompareTag("Player") || other.CompareTag("SquadMate"))
        {
            if (CanPickup(other.gameObject))
            {
                PickupItem(other.gameObject);
            }
        }
    }

    protected virtual bool CanPickup(GameObject picker)
    {
        // Override in derived classes for specific pickup conditions
        return true;
    }

    protected virtual void PickupItem(GameObject picker)
    {
        isPickedUp = true;

        // Apply item effects
        ApplyItemEffects(picker);

        // Give reward to AI
        GiveRewardToAI(picker);

        // Log pickup
        Debug.Log($"🎒 {picker.name} picked up {itemName} ({rarity} {lootType})");

        // Remove from environment
        RemoveFromEnvironment();

        // Destroy item
        Destroy(gameObject, 0.1f);
    }

    protected virtual void ApplyItemEffects(GameObject picker)
    {
        // Default implementation - override in derived classes
        Debug.Log($"🎒 {picker.name} picked up {itemName}");
    }

    protected virtual void GiveRewardToAI(GameObject picker)
    {
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            // Base reward modified by rarity
            float finalReward = rewardValue * GetRarityMultiplier();
            agent.AddReward(finalReward);

            Debug.Log($"🎯 Agent rewarded {finalReward:F2} for picking up {itemName}");
        }
    }

    protected float GetRarityMultiplier()
    {
        switch (rarity)
        {
            case LootRarity.Common: return 1f;
            case LootRarity.Uncommon: return 1.2f;
            case LootRarity.Rare: return 1.5f;
            case LootRarity.Epic: return 2f;
            case LootRarity.Legendary: return 3f;
            default: return 1f;
        }
    }

    protected virtual void RemoveFromEnvironment()
    {
        // Remove from loot tracking systems
        LootZoneManager lootManager = FindObjectOfType<LootZoneManager>();
        if (lootManager != null)
        {
            lootManager.RemoveLootItem(gameObject);
        }

        // Hide visual
        if (itemRenderer != null)
        {
            itemRenderer.enabled = false;
        }

        // Disable collider
        if (itemCollider != null)
        {
            itemCollider.enabled = false;
        }
    }

    /// <summary>
    /// Get the priority value for AI decision making
    /// </summary>
    public virtual float GetAIPriority(SquadMateAgent agent)
    {
        return itemValue * GetRarityMultiplier();
    }

    protected virtual void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = GetRarityColor();
        Gizmos.DrawWireSphere(transform.position, 2f);
    }
}
