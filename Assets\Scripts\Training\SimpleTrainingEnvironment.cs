using UnityEngine;
using Unity.MLAgents;

/// <summary>
/// Simple training environment that ensures the SquadMate agent gets proper rewards
/// Fixes common issues with zero rewards and no learning progress
/// </summary>
public class SimpleTrainingEnvironment : MonoBehaviour
{
    [Header("🎯 Training Setup")]
    public SquadMateAgent squadMateAgent;
    public Transform playerTransform;
    public Transform[] enemies;
    public Transform[] lootItems;
    
    [Header("⚙️ Environment Settings")]
    public float episodeLength = 120f; // 2 minutes per episode
    public bool autoReset = true;
    public bool debugRewards = true;
    
    [Header("📊 Training Stats")]
    public float totalReward = 0f;
    public int episodeCount = 0;
    public float averageReward = 0f;
    
    private float episodeStartTime;
    private float lastRewardCheck;
    
    void Start()
    {
        SetupTrainingEnvironment();
    }
    
    void SetupTrainingEnvironment()
    {
        Debug.Log("🎯 Setting up Simple Training Environment...");
        
        // Find or create SquadMate agent
        if (squadMateAgent == null)
        {
            squadMateAgent = FindObjectOfType<SquadMateAgent>();
            if (squadMateAgent == null)
            {
                Debug.LogError("❌ No SquadMateAgent found! Please add one to the scene.");
                return;
            }
        }
        
        // Find or create player
        if (playerTransform == null)
        {
            GameObject player = GameObject.FindWithTag("Player");
            if (player == null)
            {
                player = CreateSimplePlayer();
            }
            playerTransform = player.transform;
        }
        
        // Find or create enemies
        if (enemies == null || enemies.Length == 0)
        {
            enemies = FindEnemies();
            if (enemies.Length == 0)
            {
                CreateSimpleEnemies();
                enemies = FindEnemies();
            }
        }
        
        // Setup agent references
        SetupAgentReferences();
        
        // Start episode
        StartNewEpisode();
        
        Debug.Log($"✅ Training environment ready! Agent: {squadMateAgent.name}, Player: {playerTransform.name}, Enemies: {enemies.Length}");
    }
    
    GameObject CreateSimplePlayer()
    {
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.tag = "Player";
        player.transform.position = new Vector3(0f, 1f, 0f);
        player.transform.localScale = new Vector3(1f, 2f, 1f);
        
        // Add player controller
        PlayerController playerController = player.AddComponent<PlayerController>();
        playerController.maxHealth = 100f;
        playerController.currentHealth = 100f;
        
        // Add health system
        HealthSystem healthSystem = player.AddComponent<HealthSystem>();
        
        // Set material
        Renderer renderer = player.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = Color.blue;
        renderer.material = mat;
        
        Debug.Log("👤 Created simple player");
        return player;
    }
    
    void CreateSimpleEnemies()
    {
        for (int i = 0; i < 3; i++)
        {
            GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            enemy.name = $"Enemy_{i + 1}";
            enemy.tag = "Enemy";
            enemy.transform.position = new Vector3(
                Random.Range(-10f, 10f),
                1f,
                Random.Range(-10f, 10f)
            );
            enemy.transform.localScale = new Vector3(1f, 2f, 1f);
            
            // Add enemy AI
            EnemyAI enemyAI = enemy.AddComponent<EnemyAI>();
            enemyAI.health = 100f;
            enemyAI.maxHealth = 100f;
            
            // Add rigidbody
            Rigidbody rb = enemy.AddComponent<Rigidbody>();
            rb.freezeRotation = true;
            
            // Set material
            Renderer renderer = enemy.GetComponent<Renderer>();
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = Color.red;
            renderer.material = mat;
        }
        
        Debug.Log("👹 Created 3 simple enemies");
    }
    
    Transform[] FindEnemies()
    {
        GameObject[] enemyObjects = GameObject.FindGameObjectsWithTag("Enemy");
        Transform[] enemyTransforms = new Transform[enemyObjects.Length];
        for (int i = 0; i < enemyObjects.Length; i++)
        {
            enemyTransforms[i] = enemyObjects[i].transform;
        }
        return enemyTransforms;
    }
    
    void SetupAgentReferences()
    {
        if (squadMateAgent == null) return;
        
        // Set player reference
        squadMateAgent.player = playerTransform;
        
        // Set enemies reference
        squadMateAgent.enemies = enemies;
        
        // Ensure agent has required components
        if (squadMateAgent.GetComponent<InventorySystem>() == null)
        {
            squadMateAgent.gameObject.AddComponent<InventorySystem>();
        }
        
        if (squadMateAgent.GetComponent<HealthSystem>() == null)
        {
            squadMateAgent.gameObject.AddComponent<HealthSystem>();
        }
        
        // Set up environment reference
        GameEnvironment gameEnv = FindObjectOfType<GameEnvironment>();
        if (gameEnv == null)
        {
            gameEnv = gameObject.AddComponent<GameEnvironment>();
            gameEnv.squadMate = squadMateAgent;
        }
        squadMateAgent.environment = gameEnv;
        
        Debug.Log("🔗 Agent references configured");
    }
    
    void StartNewEpisode()
    {
        episodeStartTime = Time.time;
        lastRewardCheck = Time.time;
        episodeCount++;
        
        // Reset positions
        if (squadMateAgent != null)
        {
            squadMateAgent.transform.position = new Vector3(-2f, 1f, 0f);
        }
        
        if (playerTransform != null)
        {
            playerTransform.position = new Vector3(0f, 1f, 0f);
        }
        
        // Reset enemies
        foreach (Transform enemy in enemies)
        {
            if (enemy != null)
            {
                EnemyAI enemyAI = enemy.GetComponent<EnemyAI>();
                if (enemyAI != null)
                {
                    enemyAI.health = enemyAI.maxHealth;
                }
                
                // Randomize enemy positions
                enemy.position = new Vector3(
                    Random.Range(-10f, 10f),
                    1f,
                    Random.Range(-10f, 10f)
                );
            }
        }
        
        Debug.Log($"🔄 Episode {episodeCount} started");
    }
    
    void Update()
    {
        if (squadMateAgent == null) return;
        
        // Check episode time limit
        if (Time.time - episodeStartTime > episodeLength)
        {
            EndEpisode("Time limit reached");
        }
        
        // Debug rewards every 5 seconds
        if (debugRewards && Time.time - lastRewardCheck > 5f)
        {
            LogRewardStatus();
            lastRewardCheck = Time.time;
        }
        
        // Check for episode end conditions
        CheckEpisodeEndConditions();
    }
    
    void CheckEpisodeEndConditions()
    {
        // Check if agent died
        if (squadMateAgent.currentHealth <= 0)
        {
            EndEpisode("Agent died");
            return;
        }
        
        // Check if player died
        if (playerTransform != null)
        {
            PlayerController playerController = playerTransform.GetComponent<PlayerController>();
            if (playerController != null && playerController.currentHealth <= 0)
            {
                EndEpisode("Player died");
                return;
            }
        }
        
        // Check if all enemies are dead (success condition)
        bool allEnemiesDead = true;
        foreach (Transform enemy in enemies)
        {
            if (enemy != null)
            {
                EnemyAI enemyAI = enemy.GetComponent<EnemyAI>();
                if (enemyAI != null && enemyAI.health > 0)
                {
                    allEnemiesDead = false;
                    break;
                }
            }
        }
        
        if (allEnemiesDead)
        {
            squadMateAgent.AddReward(5f); // Big success reward
            EndEpisode("All enemies eliminated - SUCCESS!");
        }
    }
    
    void EndEpisode(string reason)
    {
        float episodeReward = squadMateAgent.GetCumulativeReward();
        totalReward += episodeReward;
        averageReward = totalReward / episodeCount;
        
        Debug.Log($"📊 Episode {episodeCount} ended: {reason}");
        Debug.Log($"💰 Episode reward: {episodeReward:F2}, Average: {averageReward:F2}");
        
        if (autoReset)
        {
            squadMateAgent.EndEpisode();
            Invoke(nameof(StartNewEpisode), 1f); // Small delay before restart
        }
    }
    
    void LogRewardStatus()
    {
        float currentReward = squadMateAgent.GetCumulativeReward();
        float episodeTime = Time.time - episodeStartTime;
        
        Debug.Log($"🎯 Episode {episodeCount} - Time: {episodeTime:F1}s, Reward: {currentReward:F2}");
        Debug.Log($"📍 Agent Health: {squadMateAgent.currentHealth:F1}, Has Weapon: {squadMateAgent.hasWeapon}");
        
        if (playerTransform != null)
        {
            float distance = Vector3.Distance(squadMateAgent.transform.position, playerTransform.position);
            Debug.Log($"👥 Distance to player: {distance:F1}m");
        }
    }
    
    /// <summary>
    /// Call this to manually give the agent a reward for testing
    /// </summary>
    [ContextMenu("Give Test Reward")]
    public void GiveTestReward()
    {
        if (squadMateAgent != null)
        {
            squadMateAgent.AddReward(1f);
            Debug.Log("🎁 Gave agent +1.0 test reward");
        }
    }
    
    /// <summary>
    /// Call this to manually end the current episode
    /// </summary>
    [ContextMenu("End Episode")]
    public void ManualEndEpisode()
    {
        EndEpisode("Manual end");
    }
}
