{"squadmate_decision_tree": {"version": "1.0", "description": "PUBG-style SquadMate AI decision tree for tactical combat and loot management", "root": {"type": "selector", "name": "main_behavior", "children": [{"type": "sequence", "name": "emergency_response", "condition": "health < 0.25 || player_downed", "children": [{"type": "action", "name": "find_cover", "priority": 10, "condition": "in_combat && health < 0.5"}, {"type": "action", "name": "heal_self", "priority": 9, "condition": "health < 0.25 && has_healing_items && !in_combat"}, {"type": "action", "name": "revive_player", "priority": 8, "condition": "player_downed && distance_to_player < 5.0"}]}, {"type": "sequence", "name": "combat_behavior", "condition": "enemies_nearby", "children": [{"type": "action", "name": "equip_best_weapon", "priority": 7, "condition": "!has_weapon || better_weapon_available"}, {"type": "action", "name": "engage_enemy", "priority": 6, "condition": "has_weapon && enemy_in_range"}, {"type": "action", "name": "flank_enemy", "priority": 5, "condition": "has_weapon && can_flank"}, {"type": "action", "name": "use_throwable", "priority": 4, "condition": "has_grenades && enemy_clustered"}]}, {"type": "sequence", "name": "loot_behavior", "condition": "!in_combat", "children": [{"type": "action", "name": "pickup_weapon", "priority": 3, "condition": "weapon_nearby && (!has_weapon || weapon_is_upgrade)", "loot_priority": {"M416": 10, "SCAR-L": 9, "Kar98k": 8, "UMP45": 6, "AKM": 7}}, {"type": "action", "name": "pickup_healing", "priority": 2, "condition": "healing_nearby && (health < 0.8 || inventory_space_available)", "loot_priority": {"Med Kit": 10, "First Aid Kit": 8, "Bandage": 5, "Energy Drink": 6, "Painkiller": 7}}, {"type": "action", "name": "pickup_armor", "priority": 1, "condition": "armor_nearby && (!has_armor || armor_is_upgrade)", "loot_priority": {"Level 3 Vest": 10, "Level 3 Helmet": 10, "Level 2 Vest": 7, "Level 2 Helmet": 7, "Level 1 Vest": 4, "Level 1 Helmet": 4}}]}, {"type": "sequence", "name": "support_behavior", "condition": "player_alive && !in_combat", "children": [{"type": "action", "name": "follow_player", "priority": 0, "condition": "distance_to_player > 10.0"}, {"type": "action", "name": "watch_flanks", "priority": 0, "condition": "distance_to_player < 5.0"}, {"type": "action", "name": "share_loot", "priority": 0, "condition": "player_needs_items && has_extra_items"}]}]}, "conditions": {"health": "current_health / max_health", "player_downed": "player.health <= 0", "in_combat": "time_since_last_damage < 10.0", "enemies_nearby": "nearest_enemy_distance < 20.0", "has_weapon": "inventory.weapon_count > 0", "has_healing_items": "inventory.healing_count > 0", "distance_to_player": "Vector3.Distance(transform.position, player.position)", "enemy_in_range": "nearest_enemy_distance < weapon_range", "can_flank": "!taking_cover && has_weapon", "weapon_nearby": "nearest_weapon_distance < 5.0", "healing_nearby": "nearest_healing_distance < 5.0", "armor_nearby": "nearest_armor_distance < 5.0"}, "rewards": {"weapon_pickup": {"M416": 0.4, "SCAR-L": 0.4, "Kar98k": 0.5, "UMP45": 0.3, "AKM": 0.35, "default": 0.2}, "healing_pickup": {"critical_health": 0.5, "low_health": 0.4, "medium_health": 0.3, "high_health": 0.2}, "combat": {"enemy_hit": 0.3, "enemy_kill": 0.5, "headshot_bonus": 0.2, "tactical_kill": 0.3, "damage_taken": -0.1, "death": -1.0}, "teamwork": {"revive_player": 0.5, "cover_player": 0.1, "share_loot": 0.2, "follow_player": 0.05}}, "parameters": {"follow_distance": 8.0, "combat_range": 20.0, "loot_range": 5.0, "cover_range": 10.0, "revive_time": 3.0, "healing_time": {"Bandage": 4.0, "First Aid Kit": 6.0, "Med Kit": 8.0, "Energy Drink": 4.0, "Painkiller": 6.0}}}}