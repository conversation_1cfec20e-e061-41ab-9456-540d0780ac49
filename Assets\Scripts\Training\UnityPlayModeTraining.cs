using UnityEngine;
using Unity.MLAgents.Policies;

/// <summary>
/// Training controller for Unity Play Mode training
/// Manages training sessions without external ML-Agents
/// </summary>
public class UnityPlayModeTraining : MonoBehaviour
{
    [Header("🎮 Training Settings")]
    public bool enableTraining = true;
    public float trainingTimeScale = 10f;
    public int maxEpisodeSteps = 5000;
    public float episodeTimeout = 300f; // 5 minutes per episode

    [Header("📊 Training Stats")]
    public int currentEpisode = 0;
    public float totalTrainingTime = 0f;
    public float averageReward = 0f;
    public float bestReward = float.MinValue;

    [Header("🎯 PUBG Training Focus")]
    public bool focusOnCombat = true;
    public bool focusOnLoot = true;
    public bool focusOnTeamwork = true;
    public bool enableDynamicDifficulty = true;

    private SquadMateAgent agent;
    private DynamicRewardSystem rewardSystem;
    private LootZoneManager lootManager;
    private float episodeStartTime;
    private float totalRewardThisEpisode;
    private int episodeCount = 0;
    private float cumulativeReward = 0f;

    void Start()
    {
        agent = FindObjectOfType<SquadMateAgent>();
        rewardSystem = FindObjectOfType<DynamicRewardSystem>();
        lootManager = FindObjectOfType<LootZoneManager>();

        if (enableTraining)
        {
            StartTraining();
        }
    }

    void StartTraining()
    {
        Debug.Log("🚀 Starting Unity Play Mode PUBG Training!");
        Debug.Log("🎯 Training Focus: Combat=" + focusOnCombat + ", Loot=" + focusOnLoot + ", Teamwork=" + focusOnTeamwork);

        // Set time scale for faster training
        Time.timeScale = trainingTimeScale;

        // Configure agent for training
        if (agent != null)
        {
            BehaviorParameters behaviorParams = agent.GetComponent<BehaviorParameters>();
            if (behaviorParams != null)
            {
                behaviorParams.BehaviorType = BehaviorType.Default; // Training mode
            }
        }

        // Start first episode
        StartNewEpisode();
    }

    void Update()
    {
        if (!enableTraining) return;

        totalTrainingTime += Time.unscaledDeltaTime;

        // Check episode timeout
        if (Time.time - episodeStartTime > episodeTimeout)
        {
            Debug.Log("⏰ Episode timeout - starting new episode");
            EndCurrentEpisode();
        }

        // Check if agent needs episode reset
        if (agent != null && ShouldResetEpisode())
        {
            EndCurrentEpisode();
        }

        // Update training stats
        UpdateTrainingStats();
    }

    bool ShouldResetEpisode()
    {
        if (agent == null) return false;

        // Reset if agent died
        if (agent.currentHealth <= 0)
        {
            return true;
        }

        // Reset if player died and agent failed to help
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null && player.isDowned)
        {
            // Give agent some time to help
            if (Time.time - episodeStartTime > 60f) // 1 minute to help
            {
                return true;
            }
        }

        // Reset if no progress for too long
        if (Time.time - episodeStartTime > episodeTimeout * 0.8f)
        {
            return true;
        }

        return false;
    }

    void StartNewEpisode()
    {
        currentEpisode++;
        episodeStartTime = Time.time;
        totalRewardThisEpisode = 0f;

        Debug.Log($"🎮 Starting Episode {currentEpisode}");

        // Reset environment
        ResetEnvironment();

        // Reset agent
        if (agent != null)
        {
            agent.EndEpisode();
        }

        // Reset reward system
        if (rewardSystem != null)
        {
            rewardSystem.ResetRewards();
        }

        // Adjust difficulty if enabled
        if (enableDynamicDifficulty)
        {
            AdjustDifficulty();
        }
    }

    void EndCurrentEpisode()
    {
        if (rewardSystem != null)
        {
            totalRewardThisEpisode = rewardSystem.GetTotalRewards();
        }

        // Update statistics
        cumulativeReward += totalRewardThisEpisode;
        episodeCount++;
        averageReward = cumulativeReward / episodeCount;

        if (totalRewardThisEpisode > bestReward)
        {
            bestReward = totalRewardThisEpisode;
            Debug.Log($"🏆 NEW BEST REWARD: {bestReward:F2} in Episode {currentEpisode}!");
        }

        Debug.Log($"📊 Episode {currentEpisode} Complete - Reward: {totalRewardThisEpisode:F2}, Average: {averageReward:F2}");

        // Start new episode
        StartNewEpisode();
    }

    void ResetEnvironment()
    {
        // Reset game environment
        GameEnvironment gameEnv = FindObjectOfType<GameEnvironment>();
        if (gameEnv != null)
        {
            gameEnv.InitializeEnvironment();
        }

        // Reset loot zones
        if (lootManager != null)
        {
            // Trigger new loot spawning
            Debug.Log("🎒 Resetting loot zones for new episode");
        }

        // Reset player
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            player.currentHealth = player.maxHealth;
            player.isDowned = false;
        }
    }

    void AdjustDifficulty()
    {
        // Increase difficulty based on performance
        if (averageReward > 5f) // Agent is doing well
        {
            // Increase enemy count
            CombatTrainingEnvironment combatEnv = FindObjectOfType<CombatTrainingEnvironment>();
            if (combatEnv != null)
            {
                combatEnv.maxEnemies = Mathf.Min(combatEnv.maxEnemies + 1, 12);
                combatEnv.enemySpawnInterval = Mathf.Max(combatEnv.enemySpawnInterval - 1f, 5f);
            }

            Debug.Log("📈 Increased difficulty - more enemies!");
        }
        else if (averageReward < -2f) // Agent is struggling
        {
            // Decrease difficulty
            CombatTrainingEnvironment combatEnv = FindObjectOfType<CombatTrainingEnvironment>();
            if (combatEnv != null)
            {
                combatEnv.maxEnemies = Mathf.Max(combatEnv.maxEnemies - 1, 3);
                combatEnv.enemySpawnInterval = Mathf.Min(combatEnv.enemySpawnInterval + 2f, 20f);
            }

            Debug.Log("📉 Decreased difficulty - fewer enemies");
        }
    }

    void UpdateTrainingStats()
    {
        // Update current episode reward
        if (rewardSystem != null)
        {
            totalRewardThisEpisode = rewardSystem.GetTotalRewards();
        }
    }

    void OnGUI()
    {
        if (!enableTraining) return;

        // Training stats display
        GUILayout.BeginArea(new Rect(10, 10, 400, 300));
        GUILayout.BeginVertical("box");

        GUILayout.Label("🎮 PUBG Training Session", GUI.skin.label);
        GUILayout.Space(5);

        GUILayout.Label($"Episode: {currentEpisode}");
        GUILayout.Label($"Training Time: {totalTrainingTime / 60f:F1} minutes");
        GUILayout.Label($"Time Scale: {Time.timeScale}x");

        GUILayout.Space(10);
        GUILayout.Label("📊 Performance:", GUI.skin.label);
        GUILayout.Label($"Current Reward: {totalRewardThisEpisode:F2}");
        GUILayout.Label($"Average Reward: {averageReward:F2}");
        GUILayout.Label($"Best Reward: {bestReward:F2}");

        GUILayout.Space(10);
        GUILayout.Label("⏱️ Episode Progress:", GUI.skin.label);
        float episodeProgress = (Time.time - episodeStartTime) / episodeTimeout;
        GUILayout.HorizontalSlider(episodeProgress, 0f, 1f);
        GUILayout.Label($"Time: {Time.time - episodeStartTime:F0}s / {episodeTimeout:F0}s");

        GUILayout.Space(10);
        if (GUILayout.Button("🔄 Reset Episode"))
        {
            EndCurrentEpisode();
        }

        if (GUILayout.Button(enableTraining ? "⏸️ Pause Training" : "▶️ Resume Training"))
        {
            enableTraining = !enableTraining;
            Time.timeScale = enableTraining ? trainingTimeScale : 1f;
        }

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }

    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            Time.timeScale = 1f; // Reset time scale when pausing
        }
        else if (enableTraining)
        {
            Time.timeScale = trainingTimeScale; // Restore training time scale
        }
    }
}
