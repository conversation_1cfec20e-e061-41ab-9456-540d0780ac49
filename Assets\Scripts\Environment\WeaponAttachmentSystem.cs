using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Advanced weapon attachment system for PUBG-style gameplay
/// Handles scopes, grips, muzzle attachments, and magazines
/// </summary>
public class WeaponAttachmentSystem : MonoBehaviour
{
    [Header("🔧 Current Attachments")]
    public WeaponAttachment currentScope;
    public WeaponAttachment currentMuzzle;
    public WeaponAttachment currentGrip;
    public WeaponAttachment currentMagazine;

    [Header("📊 Attachment Effects")]
    public float totalRecoilReduction = 0f;
    public float totalStabilityBonus = 0f;
    public float totalRangeBonus = 0f;
    public float totalDamageBonus = 0f;
    public int totalAmmoBonus = 0;

    [Header("🎯 Weapon Compatibility")]
    public string weaponType = "AR"; // AR, SMG, Sniper, DMR, Shotgun, Pistol
    public List<string> compatibleAttachments = new List<string>();

    private WeaponSystem weaponSystem;
    private SquadMateAgent agent;

    void Start()
    {
        weaponSystem = GetComponent<WeaponSystem>();
        agent = GetComponent<SquadMateAgent>();
        UpdateAttachmentEffects();
    }

    /// <summary>
    /// Attach a weapon attachment to the appropriate slot
    /// </summary>
    public bool AttachAttachment(WeaponAttachment attachment)
    {
        if (!IsCompatible(attachment))
        {
            Debug.Log($"❌ {attachment.attachmentName} is not compatible with {weaponType}");
            return false;
        }

        bool attached = false;
        WeaponAttachment oldAttachment = null;

        switch (attachment.attachmentType)
        {
            case AttachmentType.Scope:
                oldAttachment = currentScope;
                currentScope = attachment;
                attached = true;
                break;

            case AttachmentType.Muzzle:
                oldAttachment = currentMuzzle;
                currentMuzzle = attachment;
                attached = true;
                break;

            case AttachmentType.Grip:
                oldAttachment = currentGrip;
                currentGrip = attachment;
                attached = true;
                break;

            case AttachmentType.Magazine:
                oldAttachment = currentMagazine;
                currentMagazine = attachment;
                attached = true;
                break;
        }

        if (attached)
        {
            UpdateAttachmentEffects();
            
            // Reward agent for smart attachment usage
            if (agent != null)
            {
                float upgradeReward = CalculateAttachmentUpgradeReward(oldAttachment, attachment);
                agent.AddReward(upgradeReward);
                Debug.Log($"🔧 Attached {attachment.attachmentName} (Reward: +{upgradeReward:F2})");
            }

            return true;
        }

        return false;
    }

    /// <summary>
    /// Remove an attachment from a specific slot
    /// </summary>
    public WeaponAttachment RemoveAttachment(AttachmentType type)
    {
        WeaponAttachment removedAttachment = null;

        switch (type)
        {
            case AttachmentType.Scope:
                removedAttachment = currentScope;
                currentScope = null;
                break;

            case AttachmentType.Muzzle:
                removedAttachment = currentMuzzle;
                currentMuzzle = null;
                break;

            case AttachmentType.Grip:
                removedAttachment = currentGrip;
                currentGrip = null;
                break;

            case AttachmentType.Magazine:
                removedAttachment = currentMagazine;
                currentMagazine = null;
                break;
        }

        if (removedAttachment != null)
        {
            UpdateAttachmentEffects();
            Debug.Log($"🔧 Removed {removedAttachment.attachmentName}");
        }

        return removedAttachment;
    }

    /// <summary>
    /// Check if an attachment is compatible with current weapon
    /// </summary>
    public bool IsCompatible(WeaponAttachment attachment)
    {
        if (attachment.compatibleWeapons == null || attachment.compatibleWeapons.Length == 0)
            return true; // Universal attachment

        foreach (string compatibleWeapon in attachment.compatibleWeapons)
        {
            if (weaponType.Equals(compatibleWeapon, System.StringComparison.OrdinalIgnoreCase))
                return true;
        }

        return false;
    }

    /// <summary>
    /// Update total attachment effects
    /// </summary>
    private void UpdateAttachmentEffects()
    {
        totalRecoilReduction = 0f;
        totalStabilityBonus = 0f;
        totalRangeBonus = 0f;
        totalDamageBonus = 0f;
        totalAmmoBonus = 0;

        // Sum up all attachment effects
        if (currentScope != null)
        {
            totalRecoilReduction += currentScope.recoilReduction;
            totalStabilityBonus += currentScope.stabilityBonus;
            totalRangeBonus += currentScope.rangeBonus;
            totalDamageBonus += currentScope.damageBonus;
        }

        if (currentMuzzle != null)
        {
            totalRecoilReduction += currentMuzzle.recoilReduction;
            totalStabilityBonus += currentMuzzle.stabilityBonus;
            totalRangeBonus += currentMuzzle.rangeBonus;
            totalDamageBonus += currentMuzzle.damageBonus;
        }

        if (currentGrip != null)
        {
            totalRecoilReduction += currentGrip.recoilReduction;
            totalStabilityBonus += currentGrip.stabilityBonus;
            totalRangeBonus += currentGrip.rangeBonus;
            totalDamageBonus += currentGrip.damageBonus;
        }

        if (currentMagazine != null)
        {
            totalRecoilReduction += currentMagazine.recoilReduction;
            totalStabilityBonus += currentMagazine.stabilityBonus;
            totalRangeBonus += currentMagazine.rangeBonus;
            totalDamageBonus += currentMagazine.damageBonus;
            totalAmmoBonus += currentMagazine.ammoBonus;
        }

        // Apply effects to weapon system
        if (weaponSystem != null)
        {
            weaponSystem.ApplyAttachmentEffects(totalRecoilReduction, totalStabilityBonus, 
                totalRangeBonus, totalDamageBonus, totalAmmoBonus);
        }

        Debug.Log($"🔧 Attachment effects updated - Recoil: -{totalRecoilReduction:P0}, " +
                 $"Stability: +{totalStabilityBonus:P0}, Range: +{totalRangeBonus:F0}m, " +
                 $"Damage: +{totalDamageBonus:F0}, Ammo: +{totalAmmoBonus}");
    }

    /// <summary>
    /// Calculate reward for attachment upgrade
    /// </summary>
    private float CalculateAttachmentUpgradeReward(WeaponAttachment oldAttachment, WeaponAttachment newAttachment)
    {
        float baseReward = 0.1f;

        // No previous attachment - any attachment is good
        if (oldAttachment == null)
            return baseReward;

        // Calculate improvement score
        float improvementScore = 0f;

        if (newAttachment.recoilReduction > oldAttachment.recoilReduction)
            improvementScore += 0.1f;

        if (newAttachment.stabilityBonus > oldAttachment.stabilityBonus)
            improvementScore += 0.1f;

        if (newAttachment.rangeBonus > oldAttachment.rangeBonus)
            improvementScore += 0.1f;

        if (newAttachment.damageBonus > oldAttachment.damageBonus)
            improvementScore += 0.15f;

        if (newAttachment.ammoBonus > oldAttachment.ammoBonus)
            improvementScore += 0.05f;

        return baseReward + improvementScore;
    }

    /// <summary>
    /// Get attachment priority for AI decision making
    /// </summary>
    public float GetAttachmentPriority(WeaponAttachment attachment)
    {
        if (!IsCompatible(attachment))
            return 0f;

        float priority = 1f;

        // Higher priority for missing attachment slots
        switch (attachment.attachmentType)
        {
            case AttachmentType.Scope:
                if (currentScope == null) priority += 2f;
                else if (attachment.rangeBonus > currentScope.rangeBonus) priority += 1f;
                break;

            case AttachmentType.Muzzle:
                if (currentMuzzle == null) priority += 1.5f;
                else if (attachment.recoilReduction > currentMuzzle.recoilReduction) priority += 0.8f;
                break;

            case AttachmentType.Grip:
                if (currentGrip == null) priority += 1.2f;
                else if (attachment.stabilityBonus > currentGrip.stabilityBonus) priority += 0.6f;
                break;

            case AttachmentType.Magazine:
                if (currentMagazine == null) priority += 1f;
                else if (attachment.ammoBonus > currentMagazine.ammoBonus) priority += 0.5f;
                break;
        }

        // Bonus for high-tier attachments
        priority += (int)attachment.rarity * 0.3f;

        return priority;
    }

    /// <summary>
    /// Check if weapon is fully kitted
    /// </summary>
    public bool IsFullyKitted()
    {
        return currentScope != null && currentMuzzle != null && 
               currentGrip != null && currentMagazine != null;
    }

    /// <summary>
    /// Get missing attachment types
    /// </summary>
    public List<AttachmentType> GetMissingAttachments()
    {
        List<AttachmentType> missing = new List<AttachmentType>();

        if (currentScope == null) missing.Add(AttachmentType.Scope);
        if (currentMuzzle == null) missing.Add(AttachmentType.Muzzle);
        if (currentGrip == null) missing.Add(AttachmentType.Grip);
        if (currentMagazine == null) missing.Add(AttachmentType.Magazine);

        return missing;
    }
}

/// <summary>
/// Weapon attachment data structure
/// </summary>
[System.Serializable]
public class WeaponAttachment
{
    [Header("📋 Basic Info")]
    public string attachmentName;
    public AttachmentType attachmentType;
    public ItemRarity rarity = ItemRarity.Common;

    [Header("🎯 Compatibility")]
    public string[] compatibleWeapons; // AR, SMG, Sniper, etc.

    [Header("📊 Stats")]
    public float recoilReduction = 0f; // 0.0 to 1.0 (percentage)
    public float stabilityBonus = 0f; // 0.0 to 1.0 (percentage)
    public float rangeBonus = 0f; // Additional range in meters
    public float damageBonus = 0f; // Additional damage points
    public int ammoBonus = 0; // Additional ammo capacity

    [Header("🔧 Special Effects")]
    public bool reducesNoise = false; // Suppressor effect
    public bool increasesAccuracy = false; // Scope effect
    public bool reducesSpread = false; // Grip effect
    public bool fasterReload = false; // Magazine effect
}

public enum AttachmentType
{
    Scope,
    Muzzle,
    Grip,
    Magazine
}
