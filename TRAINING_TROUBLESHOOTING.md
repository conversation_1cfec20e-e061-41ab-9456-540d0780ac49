# 🔧 SquadMate AI Training Troubleshooting Guide

## ❌ Problem: Zero Rewards & No Learning Progress

### 🎯 **Quick Fix Steps**

#### 1. **Create Proper Training Scene**
```csharp
// In Unity Editor
SquadMate AI → 🎯 Create Simple PUBG Scene
```

This automatically creates:
- ✅ SquadMate agent with proper components
- ✅ Player character
- ✅ Enemy bots
- ✅ Test loot items with immediate rewards
- ✅ Training environment manager
- ✅ Automatic episode management

#### 2. **Verify Scene Setup**
Check that your scene has these components:

**SquadMate Agent GameObject:**
- ✅ `SquadMateAgent` script
- ✅ `InventorySystem` script
- ✅ `HealthSystem` script
- ✅ `BehaviorParameters` (ML-Agents)
- ✅ `Rigidbody` with freeze rotation

**Training Arena GameObject:**
- ✅ `SimpleTrainingEnvironment` script
- ✅ `TestLootSpawner` script
- ✅ `GameEnvironment` script

**Player GameObject:**
- ✅ Tag: "Player"
- ✅ `PlayerController` script
- ✅ `HealthSystem` script

**Enemy GameObjects:**
- ✅ Tag: "Enemy"
- ✅ `EnemyAI` script

#### 3. **Test Rewards Manually**
1. Play the scene in Unity
2. Select the Training Arena
3. In Inspector, click "Give Test Reward"
4. Check Console for reward messages

#### 4. **Check Training Configuration**

**BehaviorParameters Settings:**
```
Behavior Name: SquadMate
Behavior Type: Default
Team ID: 0
Use Child Sensors: ✅ True
```

**Action Space:**
- Continuous Actions: 3 (moveX, moveZ, rotate)
- Discrete Actions: 1 branch with 8 actions

#### 5. **Start Training with Correct Command**
```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=squadmate_test_v1 --force
```

### 🔍 **Detailed Diagnostics**

#### Check 1: Agent Receiving Actions
```csharp
// In SquadMateAgent.OnActionReceived(), add debug:
Debug.Log($"Action received: Move({moveX:F2}, {moveZ:F2}), Action: {actionType}");
```

#### Check 2: Rewards Being Added
```csharp
// In SquadMateAgent.CalculateRewards(), add debug:
Debug.Log($"Frame reward: {GetCumulativeReward():F3}");
```

#### Check 3: Episodes Starting/Ending
```csharp
// In SquadMateAgent.OnEpisodeBegin(), add debug:
Debug.Log($"Episode {episodeCount} started - Health: {currentHealth}");
```

### 🎯 **Common Issues & Solutions**

#### Issue 1: "No rewards showing in TensorBoard"
**Solution:**
- Ensure `SimpleTrainingEnvironment` is attached to scene
- Check Console for reward debug messages
- Verify agent has `BehaviorParameters` component

#### Issue 2: "Agent not moving"
**Solution:**
- Check `Rigidbody` is not kinematic
- Verify `moveSpeed` > 0 in SquadMateAgent
- Ensure no physics constraints

#### Issue 3: "Episodes not ending"
**Solution:**
- `SimpleTrainingEnvironment` automatically manages episodes (120s each)
- Check `autoReset = true` in training environment
- Verify enemies have `EnemyAI` component

#### Issue 4: "Agent not picking up loot"
**Solution:**
- Test loot items have `RewardTestLoot` script
- Colliders are set as triggers
- Agent has "Agent" tag

### 📊 **Expected Training Progress**

#### First 1000 Steps:
- ✅ Agent should move randomly
- ✅ Occasional loot pickup rewards (+0.3 to +0.5)
- ✅ Basic survival rewards (+0.01 per frame)
- ✅ Episodes ending after 120 seconds

#### After 10k Steps:
- ✅ More directed movement toward loot
- ✅ Better formation keeping with player
- ✅ Improved reward accumulation

#### After 50k Steps:
- ✅ Consistent loot collection
- ✅ Basic combat engagement
- ✅ Tactical positioning

### 🛠️ **Debug Commands**

#### In Unity Console:
```csharp
// Get current reward
Debug.Log($"Current reward: {FindObjectOfType<SquadMateAgent>().GetCumulativeReward()}");

// Force episode end
FindObjectOfType<SimpleTrainingEnvironment>().ManualEndEpisode();

// Spawn test loot
FindObjectOfType<TestLootSpawner>().SpawnTestLoot();
```

#### In Training Terminal:
```bash
# Check if ML-Agents is receiving data
mlagents-learn --help

# Verify config file
cat config/squadmate-ppo.yaml

# Check TensorBoard
tensorboard --logdir results
```

### 🎮 **Manual Testing**

1. **Play Mode Testing:**
   - Press Play in Unity
   - Use WASD to control agent manually (if Heuristic enabled)
   - Walk into test loot items
   - Check Console for reward messages

2. **Reward Verification:**
   - Each loot pickup should show: "Agent picked up TestWeapon_1 - Reward: +0.5"
   - Formation keeping should give small continuous rewards
   - Combat should trigger engagement rewards

3. **Episode Management:**
   - Episodes should auto-restart every 2 minutes
   - Check Console for "Episode X started" messages
   - Verify agent position resets

### 🚨 **Emergency Reset**

If nothing works, try this complete reset:

1. **Delete existing scene**
2. **Create new scene**
3. **Run:** `SquadMate AI → 🎯 Create Simple PUBG Scene`
4. **Save scene as:** `PUBGTrainingArena.unity`
5. **Start training:** `mlagents-learn config/squadmate-ppo.yaml --run-id=fresh_start --force`

### 📞 **Still Having Issues?**

Check these files for proper setup:
- ✅ `SimpleTrainingEnvironment.cs` - Episode management
- ✅ `RewardTestLoot.cs` - Immediate reward items
- ✅ `InventorySystem.cs` - Loot pickup system
- ✅ `SquadMateAgent.cs` - Main agent logic

**Expected Console Output:**
```
🎯 Setting up Simple Training Environment...
👤 Created simple player
👹 Created 3 simple enemies
🔗 Agent references and training environment configured
🎁 Spawned 3 weapons and 5 healing items
🔄 Episode 1 started
🎯 Agent picked up TestWeapon_1 - Reward: +0.5
🔫 TeammateBot now has a weapon!
```

If you see these messages, your training environment is working correctly! 🎉
