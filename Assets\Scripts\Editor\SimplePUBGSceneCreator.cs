using UnityEngine;
using UnityEditor;
using Unity.MLAgents;
using Unity.MLAgents.Policies;
using UnityEngine.AI;
using UnityEditor.SceneManagement;

/// <summary>
/// Simplified PUBG scene creator that works with any Unity setup
/// </summary>
public class SimplePUBGSceneCreator : EditorWindow
{
    [MenuItem("SquadMate AI/🎯 Create Simple PUBG Scene")]
    public static void ShowWindow()
    {
        GetWindow<SimplePUBGSceneCreator>("Simple PUBG Setup");
    }

    void OnGUI()
    {
        GUILayout.Label("🎮 Simple PUBG Training Scene", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This creates a basic PUBG training environment:");
        GUILayout.Label("✅ Terrain with buildings");
        GUILayout.Label("✅ Player and SquadMate with ML-Agents");
        GUILayout.Label("✅ Basic loot system");
        GUILayout.Label("✅ Enemy AI");
        GUILayout.Label("✅ Ready for training");

        GUILayout.Space(20);

        if (GUILayout.Button("🚀 CREATE SIMPLE PUBG SCENE", GUILayout.Height(50)))
        {
            CreateSimplePUBGScene();
        }

        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This creates a simplified version that works with any Unity setup.", MessageType.Info);
    }

    public static void CreateSimplePUBGScene()
    {
        Debug.Log("🚀 Creating Simple PUBG Scene...");

        try
        {
            // Step 1: Clear scene
            ClearScene();

            // Step 2: Create terrain
            CreateSimpleTerrain();

            // Step 3: Create lighting
            CreateLighting();

            // Step 4: Create player
            GameObject player = CreatePlayer();

            // Step 5: Create squadmate
            GameObject squadmate = CreateSquadMate();

            // Step 6: Create environment
            GameObject environment = CreateEnvironment();

            // Step 7: Create basic loot
            CreateBasicLoot();

            // Step 8: Create enemies
            CreateEnemies();

            // Step 9: Configure references
            ConfigureReferences(player, squadmate, environment);

            // Step 10: Bake NavMesh
            BakeNavMesh();

            // Step 11: Save scene
            SaveScene();

            Debug.Log("🎉 Simple PUBG Scene created successfully!");
            EditorUtility.DisplayDialog("Success!",
                "Simple PUBG training scene created!\n\n" +
                "✅ All systems ready\n" +
                "✅ Press Play to test\n" +
                "✅ Ready for ML-Agents training", "Great!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error creating scene: {e.Message}");
            EditorUtility.DisplayDialog("Error", $"Failed to create scene:\n{e.Message}", "OK");
        }
    }

    private static void ClearScene()
    {
        Debug.Log("🧹 Clearing scene...");
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name != "Main Camera" && obj.transform.parent == null)
            {
                Object.DestroyImmediate(obj);
            }
        }
    }

    private static void CreateSimpleTerrain()
    {
        Debug.Log("🌍 Creating terrain...");

        GameObject terrain = GameObject.CreatePrimitive(PrimitiveType.Plane);
        terrain.name = "Training Ground";
        terrain.transform.localScale = new Vector3(10, 1, 10);
        terrain.transform.position = Vector3.zero;

        // Make it static for NavMesh
        GameObjectUtility.SetStaticEditorFlags(terrain, StaticEditorFlags.NavigationStatic);
    }

    private static void CreateLighting()
    {
        Debug.Log("💡 Creating lighting...");

        GameObject light = new GameObject("Directional Light");
        Light lightComp = light.AddComponent<Light>();
        lightComp.type = LightType.Directional;
        lightComp.intensity = 1.5f;
        light.transform.rotation = Quaternion.Euler(45f, 45f, 0f);
    }

    private static GameObject CreatePlayer()
    {
        Debug.Log("👤 Creating player...");

        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        player.tag = "Player";

        // Add components
        Rigidbody rb = player.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        player.AddComponent<PlayerController>();
        player.AddComponent<InventorySystem>();
        player.AddComponent<HealthSystem>();

        // Set color
        Renderer renderer = player.GetComponent<Renderer>();
        renderer.material.color = Color.blue;

        return player;
    }

    private static GameObject CreateSquadMate()
    {
        Debug.Log("🤖 Creating squadmate...");

        GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        squadmate.name = "SquadMate";
        squadmate.transform.position = new Vector3(3, 1, 0);
        squadmate.tag = "Agent";

        // Add rigidbody
        Rigidbody rb = squadmate.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Add ML-Agents components
        BehaviorParameters behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;

        // Add SquadMate components
        squadmate.AddComponent<SquadMateAgent>();
        squadmate.AddComponent<InventorySystem>();
        squadmate.AddComponent<HealthSystem>();
        squadmate.AddComponent<WeaponSystem>();
        squadmate.AddComponent<DynamicRewardSystem>();

        // Set color
        Renderer renderer = squadmate.GetComponent<Renderer>();
        renderer.material.color = Color.green;

        return squadmate;
    }

    private static GameObject CreateEnvironment()
    {
        Debug.Log("🌍 Creating environment...");

        GameObject environment = new GameObject("Environment");
        environment.AddComponent<GameEnvironment>();
        environment.AddComponent<SimpleTrainingEnvironment>();
        environment.AddComponent<ObjectSpawner>();

        return environment;
    }

    private static void CreateBasicLoot()
    {
        Debug.Log("📦 Creating basic loot...");

        // Create some medkits
        for (int i = 0; i < 5; i++)
        {
            Vector3 pos = new Vector3(Random.Range(-20f, 20f), 1f, Random.Range(-20f, 20f));
            GameObject medkit = GameObject.CreatePrimitive(PrimitiveType.Cube);
            medkit.name = "Medkit";
            medkit.transform.position = pos;
            medkit.transform.localScale = Vector3.one * 0.5f;
            medkit.GetComponent<Collider>().isTrigger = true;
            medkit.AddComponent<MedkitPickup>();
            medkit.GetComponent<Renderer>().material.color = Color.cyan;
        }

        // Create some weapons
        for (int i = 0; i < 3; i++)
        {
            Vector3 pos = new Vector3(Random.Range(-20f, 20f), 1f, Random.Range(-20f, 20f));
            GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            weapon.name = "Weapon";
            weapon.transform.position = pos;
            weapon.transform.localScale = new Vector3(0.3f, 1f, 0.3f);
            weapon.GetComponent<Collider>().isTrigger = true;
            weapon.AddComponent<WeaponPickup>();
            weapon.GetComponent<Renderer>().material.color = Color.yellow;
        }
    }

    private static void CreateEnemies()
    {
        Debug.Log("🎯 Creating enemies...");

        for (int i = 0; i < 3; i++)
        {
            Vector3 pos = new Vector3(Random.Range(-15f, 15f), 1f, Random.Range(-15f, 15f));
            GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            enemy.name = "Enemy";
            enemy.transform.position = pos;
            enemy.tag = "Enemy";

            // Add NavMesh agent
            NavMeshAgent navAgent = enemy.AddComponent<NavMeshAgent>();
            navAgent.speed = 3f;
            navAgent.stoppingDistance = 5f;

            enemy.AddComponent<EnemyController>();
            enemy.AddComponent<HealthSystem>();

            // Set color
            enemy.GetComponent<Renderer>().material.color = Color.red;
        }
    }

    private static void ConfigureReferences(GameObject player, GameObject squadmate, GameObject environment)
    {
        Debug.Log("🔗 Configuring references...");

        // Configure SquadMate
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.player = player.transform;
            agent.environment = environment.GetComponent<GameEnvironment>();
        }

        // Configure Environment
        GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
        if (gameEnv != null)
        {
            gameEnv.player = player.transform;
            gameEnv.squadMate = agent;
            gameEnv.environmentSize = new Vector3(50f, 0f, 50f);
        }
    }

    private static void BakeNavMesh()
    {
        Debug.Log("🗺️ Baking NavMesh...");

        try
        {
            // Use Unity's built-in NavMesh baking
            UnityEditor.AI.NavMeshBuilder.BuildNavMesh();
            Debug.Log("✅ NavMesh baked successfully!");
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"⚠️ NavMesh baking failed: {e.Message}");
            Debug.LogWarning("You can manually bake NavMesh using: Window → AI → Navigation");
        }
    }

    private static void SaveScene()
    {
        Debug.Log("💾 Saving scene...");

        if (!AssetDatabase.IsValidFolder("Assets/Scenes"))
            AssetDatabase.CreateFolder("Assets", "Scenes");

        EditorSceneManager.SaveScene(
            EditorSceneManager.GetActiveScene(),
            "Assets/Scenes/SimplePUBGTraining.unity"
        );

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
}
