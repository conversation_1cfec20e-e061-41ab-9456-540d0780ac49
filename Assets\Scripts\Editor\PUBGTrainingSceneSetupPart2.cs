using UnityEngine;
using UnityEditor;
using Unity.MLAgents;
using Unity.MLAgents.Policies;
using UnityEngine.AI;
using UnityEditor.SceneManagement;
using System.Collections.Generic;

/// <summary>
/// Part 2 of PUBG Training Scene Setup - Additional methods
/// </summary>
public static class PUBGTrainingSceneSetupPart2
{
    public static void CreatePUBGLootZones(GameObject environment)
    {
        Debug.Log("📦 Creating PUBG loot zones...");

        GameObject lootZonesParent = new GameObject("Loot Zones");
        lootZonesParent.transform.SetParent(environment.transform);

        // Define loot zone positions and types
        var lootZoneData = new[]
        {
            new { pos = new Vector3(50, 0, 50), type = LootZoneManager.LootZoneType.Military, name = "Military Base" },
            new { pos = new Vector3(-50, 0, 50), type = LootZoneManager.LootZoneType.Medical, name = "Hospital" },
            new { pos = new Vector3(50, 0, -50), type = LootZoneManager.LootZoneType.Residential, name = "Apartments" },
            new { pos = new Vector3(-50, 0, -50), type = LootZoneManager.LootZoneType.Industrial, name = "Factory" },
            new { pos = new Vector3(0, 0, 0), type = LootZoneManager.LootZoneType.HighTier, name = "Supply Drop" },
            new { pos = new Vector3(80, 0, 20), type = LootZoneManager.LootZoneType.Mixed, name = "School" },
            new { pos = new Vector3(-80, 0, -20), type = LootZoneManager.LootZoneType.Mixed, name = "Prison" }
        };

        Material lootZoneMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/LootZoneMaterial.mat");

        foreach (var zone in lootZoneData)
        {
            GameObject lootZone = new GameObject($"LootZone_{zone.name}");
            lootZone.transform.position = zone.pos;
            lootZone.transform.SetParent(lootZonesParent.transform);

            // Add visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            indicator.name = "Zone Indicator";
            indicator.transform.SetParent(lootZone.transform);
            indicator.transform.localPosition = Vector3.zero;
            indicator.transform.localScale = new Vector3(20, 0.1f, 20);

            if (lootZoneMat != null)
                indicator.GetComponent<Renderer>().sharedMaterial = lootZoneMat;

            Object.DestroyImmediate(indicator.GetComponent<Collider>());
        }
    }

    public static void CreatePUBGEnemySystem(GameObject environment)
    {
        Debug.Log("🎯 Creating PUBG enemy system...");

        GameObject enemySpawnsParent = new GameObject("Enemy Spawns");
        enemySpawnsParent.transform.SetParent(environment.transform);

        // Create enemy spawn points around the map
        Vector3[] enemySpawnPositions = new Vector3[]
        {
            new Vector3(30, 0, 30), new Vector3(-30, 0, 30), new Vector3(30, 0, -30), new Vector3(-30, 0, -30),
            new Vector3(60, 0, 10), new Vector3(-60, 0, -10), new Vector3(10, 0, 60), new Vector3(-10, 0, -60),
            new Vector3(40, 0, -20), new Vector3(-40, 0, 20), new Vector3(20, 0, 40), new Vector3(-20, 0, -40)
        };

        for (int i = 0; i < enemySpawnPositions.Length; i++)
        {
            GameObject enemySpawn = new GameObject($"EnemySpawn_{i + 1}");
            enemySpawn.transform.position = enemySpawnPositions[i];
            enemySpawn.transform.SetParent(enemySpawnsParent.transform);

            // Add visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            indicator.name = "Enemy Indicator";
            indicator.transform.SetParent(enemySpawn.transform);
            indicator.transform.localPosition = Vector3.zero;
            indicator.transform.localScale = Vector3.one * 0.5f;

            Material enemyMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/EnemyMaterial.mat");
            if (enemyMat != null)
                indicator.GetComponent<Renderer>().sharedMaterial = enemyMat;

            Object.DestroyImmediate(indicator.GetComponent<Collider>());
        }
    }

    public static void CreateAllPUBGPrefabs()
    {
        Debug.Log("📦 Creating all PUBG prefabs...");

        if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
            AssetDatabase.CreateFolder("Assets", "Prefabs");

        if (!AssetDatabase.IsValidFolder("Assets/Prefabs/PUBG"))
            AssetDatabase.CreateFolder("Assets/Prefabs", "PUBG");

        CreatePUBGEnemyPrefab();
        CreatePUBGWeaponPrefabs();
        CreatePUBGHealingPrefabs();
        CreatePUBGArmorPrefabs();
        CreatePUBGAttachmentPrefabs();

        AssetDatabase.SaveAssets();
    }

    private static void CreatePUBGEnemyPrefab()
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "PUBG Enemy";
        enemy.transform.localScale = new Vector3(1, 2, 1);

        NavMeshAgent navAgent = enemy.AddComponent<NavMeshAgent>();
        navAgent.speed = 4f;
        navAgent.stoppingDistance = 8f;
        navAgent.acceleration = 12f;

        enemy.AddComponent<EnemyController>();
        enemy.AddComponent<EnemyAI>();
        enemy.AddComponent<HealthSystem>();
        enemy.AddComponent<WeaponSystem>();

        Material enemyMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/EnemyMaterial.mat");
        if (enemyMat != null)
            enemy.GetComponent<Renderer>().sharedMaterial = enemyMat;

        PrefabUtility.SaveAsPrefabAsset(enemy, "Assets/Prefabs/PUBG/PUBGEnemy.prefab");
        Object.DestroyImmediate(enemy);
    }

    private static void CreatePUBGWeaponPrefabs()
    {
        // M416 Assault Rifle
        CreateWeaponPrefab("M416", PrimitiveType.Cylinder, new Vector3(0.3f, 1.2f, 0.3f),
            "High-tier assault rifle with excellent range and damage");

        // UMP45 SMG
        CreateWeaponPrefab("UMP45", PrimitiveType.Cylinder, new Vector3(0.25f, 0.8f, 0.25f),
            "Reliable SMG with good close-range damage");

        // Kar98k Sniper
        CreateWeaponPrefab("Kar98k", PrimitiveType.Cylinder, new Vector3(0.2f, 1.5f, 0.2f),
            "Bolt-action sniper rifle with devastating damage");

        // SCAR-L Assault Rifle
        CreateWeaponPrefab("SCAR-L", PrimitiveType.Cylinder, new Vector3(0.3f, 1.1f, 0.3f),
            "Versatile assault rifle with balanced stats");

        // AKM Assault Rifle
        CreateWeaponPrefab("AKM", PrimitiveType.Cylinder, new Vector3(0.35f, 1.0f, 0.35f),
            "High damage assault rifle with strong recoil");
    }

    private static void CreateWeaponPrefab(string weaponName, PrimitiveType shape, Vector3 scale, string description)
    {
        GameObject weapon = GameObject.CreatePrimitive(shape);
        weapon.name = weaponName;
        weapon.transform.localScale = scale;
        weapon.GetComponent<Collider>().isTrigger = true;

        weapon.AddComponent<WeaponPickup>();
        weapon.AddComponent<PUBGLootPickup>();
        weapon.AddComponent<RotateObject>();

        Material weaponMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/WeaponMaterial.mat");
        if (weaponMat != null)
            weapon.GetComponent<Renderer>().sharedMaterial = weaponMat;

        PrefabUtility.SaveAsPrefabAsset(weapon, $"Assets/Prefabs/PUBG/{weaponName}.prefab");
        Object.DestroyImmediate(weapon);
    }

    private static void CreatePUBGHealingPrefabs()
    {
        // Med Kit
        CreateHealingPrefab("MedKit", PrimitiveType.Cube, new Vector3(0.8f, 0.4f, 0.8f),
            "Restores full health over 8 seconds");

        // First Aid Kit
        CreateHealingPrefab("FirstAidKit", PrimitiveType.Cube, new Vector3(0.6f, 0.3f, 0.6f),
            "Restores 75% health over 6 seconds");

        // Bandage
        CreateHealingPrefab("Bandage", PrimitiveType.Cylinder, new Vector3(0.3f, 0.1f, 0.3f),
            "Restores 10% health over 3 seconds");

        // Energy Drink
        CreateHealingPrefab("EnergyDrink", PrimitiveType.Cylinder, new Vector3(0.2f, 0.4f, 0.2f),
            "Provides boost effect over time");

        // Painkiller
        CreateHealingPrefab("Painkiller", PrimitiveType.Cube, new Vector3(0.3f, 0.1f, 0.2f),
            "Provides strong boost effect");
    }

    private static void CreateHealingPrefab(string itemName, PrimitiveType shape, Vector3 scale, string description)
    {
        GameObject healing = GameObject.CreatePrimitive(shape);
        healing.name = itemName;
        healing.transform.localScale = scale;
        healing.GetComponent<Collider>().isTrigger = true;

        healing.AddComponent<MedkitPickup>();
        healing.AddComponent<PUBGLootPickup>();
        healing.AddComponent<RotateObject>();

        Material medkitMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/MedkitMaterial.mat");
        if (medkitMat != null)
            healing.GetComponent<Renderer>().sharedMaterial = medkitMat;

        PrefabUtility.SaveAsPrefabAsset(healing, $"Assets/Prefabs/PUBG/{itemName}.prefab");
        Object.DestroyImmediate(healing);
    }

    private static void CreatePUBGArmorPrefabs()
    {
        // Level 3 Helmet
        CreateArmorPrefab("Level3Helmet", PrimitiveType.Sphere, new Vector3(0.8f, 0.6f, 0.8f),
            "Provides maximum head protection");

        // Level 3 Vest
        CreateArmorPrefab("Level3Vest", PrimitiveType.Cube, new Vector3(1.2f, 0.8f, 0.4f),
            "Provides maximum body protection");

        // Level 2 Helmet
        CreateArmorPrefab("Level2Helmet", PrimitiveType.Sphere, new Vector3(0.7f, 0.5f, 0.7f),
            "Provides good head protection");

        // Level 2 Vest
        CreateArmorPrefab("Level2Vest", PrimitiveType.Cube, new Vector3(1.0f, 0.7f, 0.3f),
            "Provides good body protection");
    }

    private static void CreateArmorPrefab(string armorName, PrimitiveType shape, Vector3 scale, string description)
    {
        GameObject armor = GameObject.CreatePrimitive(shape);
        armor.name = armorName;
        armor.transform.localScale = scale;
        armor.GetComponent<Collider>().isTrigger = true;

        armor.AddComponent<PUBGLootPickup>();
        armor.AddComponent<RotateObject>();

        Material armorMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/ArmorMaterial.mat");
        if (armorMat != null)
            armor.GetComponent<Renderer>().sharedMaterial = armorMat;

        PrefabUtility.SaveAsPrefabAsset(armor, $"Assets/Prefabs/PUBG/{armorName}.prefab");
        Object.DestroyImmediate(armor);
    }

    private static void CreatePUBGAttachmentPrefabs()
    {
        // 4x Scope
        CreateAttachmentPrefab("4xScope", PrimitiveType.Cylinder, new Vector3(0.2f, 0.3f, 0.2f),
            "Provides 4x magnification for long-range combat");

        // Red Dot Sight
        CreateAttachmentPrefab("RedDotSight", PrimitiveType.Cube, new Vector3(0.15f, 0.1f, 0.15f),
            "Improves close-range accuracy");

        // Compensator
        CreateAttachmentPrefab("Compensator", PrimitiveType.Cylinder, new Vector3(0.1f, 0.4f, 0.1f),
            "Reduces recoil significantly");

        // Suppressor
        CreateAttachmentPrefab("Suppressor", PrimitiveType.Cylinder, new Vector3(0.12f, 0.5f, 0.12f),
            "Reduces noise and muzzle flash");
    }

    private static void CreateAttachmentPrefab(string attachmentName, PrimitiveType shape, Vector3 scale, string description)
    {
        GameObject attachment = GameObject.CreatePrimitive(shape);
        attachment.name = attachmentName;
        attachment.transform.localScale = scale;
        attachment.GetComponent<Collider>().isTrigger = true;

        attachment.AddComponent<PUBGLootPickup>();
        attachment.AddComponent<RotateObject>();

        Material attachmentMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/AttachmentMaterial.mat");
        if (attachmentMat != null)
            attachment.GetComponent<Renderer>().sharedMaterial = attachmentMat;

        PrefabUtility.SaveAsPrefabAsset(attachment, $"Assets/Prefabs/PUBG/{attachmentName}.prefab");
        Object.DestroyImmediate(attachment);
    }
}
