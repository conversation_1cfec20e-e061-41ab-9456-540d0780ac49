using UnityEngine;

/// <summary>
/// Simple loot items that give immediate rewards for testing the training system
/// </summary>
public class RewardTestLoot : MonoBehaviour
{
    [Header("🎁 Reward Settings")]
    public float rewardValue = 0.5f;
    public string itemName = "Test Weapon";
    public bool destroyOnPickup = true;
    public bool respawn = true;
    public float respawnTime = 30f;
    
    [Header("🎨 Visual Settings")]
    public Color itemColor = Color.yellow;
    public bool enableFloating = true;
    public float floatSpeed = 2f;
    public float floatHeight = 0.2f;
    
    private Vector3 startPosition;
    private bool isPickedUp = false;
    private Renderer itemRenderer;
    
    void Start()
    {
        startPosition = transform.position;
        itemRenderer = GetComponent<Renderer>();
        
        // Set color
        if (itemRenderer != null)
        {
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = itemColor;
            itemRenderer.material = mat;
        }
        
        // Ensure collider is trigger
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.isTrigger = true;
        }
        
        Debug.Log($"🎁 {itemName} ready - Reward: +{rewardValue}");
    }
    
    void Update()
    {
        if (isPickedUp) return;
        
        // Floating animation
        if (enableFloating)
        {
            float newY = startPosition.y + Mathf.Sin(Time.time * floatSpeed) * floatHeight;
            transform.position = new Vector3(startPosition.x, newY, startPosition.z);
            transform.Rotate(0, 50f * Time.deltaTime, 0);
        }
    }
    
    private void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;
        
        if (other.CompareTag("Agent") || other.CompareTag("Player"))
        {
            SquadMateAgent agent = other.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                // Give immediate reward
                agent.AddReward(rewardValue);
                Debug.Log($"🎯 {other.name} picked up {itemName} - Reward: +{rewardValue}");
                
                // Update agent state if it's a weapon
                if (itemName.ToLower().Contains("weapon"))
                {
                    agent.hasWeapon = true;
                    Debug.Log($"🔫 {other.name} now has a weapon!");
                }
                
                // Add to inventory if available
                InventorySystem inventory = other.GetComponent<InventorySystem>();
                if (inventory != null)
                {
                    if (itemName.ToLower().Contains("weapon"))
                    {
                        inventory.AddWeapon(itemName);
                    }
                    else if (itemName.ToLower().Contains("heal") || itemName.ToLower().Contains("med"))
                    {
                        inventory.AddHealing(itemName);
                    }
                }
                
                PickupItem();
            }
        }
    }
    
    void PickupItem()
    {
        isPickedUp = true;
        
        if (destroyOnPickup)
        {
            if (respawn)
            {
                // Hide and respawn later
                gameObject.SetActive(false);
                Invoke(nameof(RespawnItem), respawnTime);
            }
            else
            {
                // Destroy permanently
                Destroy(gameObject);
            }
        }
    }
    
    void RespawnItem()
    {
        isPickedUp = false;
        transform.position = startPosition;
        gameObject.SetActive(true);
        Debug.Log($"🔄 {itemName} respawned");
    }
    
    /// <summary>
    /// Create a test loot item at the specified position
    /// </summary>
    public static GameObject CreateTestLoot(string name, Vector3 position, float reward, Color color)
    {
        GameObject loot = GameObject.CreatePrimitive(PrimitiveType.Cube);
        loot.name = $"{name}_TestLoot";
        loot.transform.position = position;
        loot.transform.localScale = new Vector3(0.5f, 0.5f, 0.5f);
        
        RewardTestLoot lootScript = loot.AddComponent<RewardTestLoot>();
        lootScript.itemName = name;
        lootScript.rewardValue = reward;
        lootScript.itemColor = color;
        
        // Ensure collider is trigger
        loot.GetComponent<Collider>().isTrigger = true;
        
        Debug.Log($"✅ Created {name} test loot at {position} (Reward: +{reward})");
        return loot;
    }
}

/// <summary>
/// Helper script to spawn test loot items around the training area
/// </summary>
public class TestLootSpawner : MonoBehaviour
{
    [Header("🎁 Loot Spawning")]
    public int weaponCount = 3;
    public int healingCount = 5;
    public float spawnRadius = 15f;
    
    [Header("🎯 Reward Values")]
    public float weaponReward = 0.5f;
    public float healingReward = 0.3f;
    
    void Start()
    {
        SpawnTestLoot();
    }
    
    [ContextMenu("Spawn Test Loot")]
    public void SpawnTestLoot()
    {
        // Spawn weapons
        for (int i = 0; i < weaponCount; i++)
        {
            Vector3 pos = GetRandomSpawnPosition();
            RewardTestLoot.CreateTestLoot($"TestWeapon_{i + 1}", pos, weaponReward, Color.blue);
        }
        
        // Spawn healing items
        for (int i = 0; i < healingCount; i++)
        {
            Vector3 pos = GetRandomSpawnPosition();
            RewardTestLoot.CreateTestLoot($"TestMedkit_{i + 1}", pos, healingReward, Color.green);
        }
        
        Debug.Log($"🎁 Spawned {weaponCount} weapons and {healingCount} healing items");
    }
    
    Vector3 GetRandomSpawnPosition()
    {
        Vector2 randomCircle = Random.insideUnitCircle * spawnRadius;
        return new Vector3(randomCircle.x, 0.5f, randomCircle.y);
    }
    
    [ContextMenu("Clear Test Loot")]
    public void ClearTestLoot()
    {
        RewardTestLoot[] allLoot = FindObjectsOfType<RewardTestLoot>();
        foreach (RewardTestLoot loot in allLoot)
        {
            DestroyImmediate(loot.gameObject);
        }
        Debug.Log($"🗑️ Cleared {allLoot.Length} test loot items");
    }
}
