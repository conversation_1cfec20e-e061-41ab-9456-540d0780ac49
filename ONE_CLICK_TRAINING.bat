@echo off
title SquadMate AI - One Click Training
color 0A

echo.
echo  ███████╗ ██████╗ ██╗   ██╗ █████╗ ██████╗ ███╗   ███╗ █████╗ ████████╗███████╗
echo  ██╔════╝██╔═══██╗██║   ██║██╔══██╗██╔══██╗████╗ ████║██╔══██╗╚══██╔══╝██╔════╝
echo  ███████╗██║   ██║██║   ██║███████║██║  ██║██╔████╔██║███████║   ██║   █████╗  
echo  ╚════██║██║▄▄ ██║██║   ██║██╔══██║██║  ██║██║╚██╔╝██║██╔══██║   ██║   ██╔══╝  
echo  ███████║╚██████╔╝╚██████╔╝██║  ██║██████╔╝██║ ╚═╝ ██║██║  ██║   ██║   ███████╗
echo  ╚══════╝ ╚══▀▀═╝  ╚═════╝ ╚═╝  ╚═╝╚═════╝ ╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝
echo.
echo                           🚀 ONE CLICK TRAINING 🚀
echo                        ================================
echo.

REM Check if Python is installed
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! 
    echo.
    echo 📥 Please install Python 3.8+ from: https://www.python.org/downloads/
    echo    Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python %python_version% found

REM Check if ML-Agents is installed
echo 🔍 Checking ML-Agents installation...
pip show mlagents >nul 2>&1
if errorlevel 1 (
    echo 📦 ML-Agents not found. Installing...
    echo.
    pip install mlagents
    if errorlevel 1 (
        echo ❌ Failed to install ML-Agents
        echo.
        echo 💡 Try running as administrator or check your internet connection
        pause
        exit /b 1
    )
    echo ✅ ML-Agents installed successfully!
) else (
    echo ✅ ML-Agents already installed
)

REM Create directories
echo 📁 Setting up directories...
if not exist "config" mkdir config
if not exist "results" mkdir results

REM Create training config
echo 📝 Creating training configuration...
(
echo behaviors:
echo   SquadMate:
echo     trainer_type: ppo
echo     hyperparameters:
echo       batch_size: 1024
echo       buffer_size: 10240
echo       learning_rate: 3.0e-4
echo       beta: 5.0e-3
echo       epsilon: 0.2
echo       lambd: 0.95
echo       num_epoch: 3
echo       learning_rate_schedule: linear
echo     network_settings:
echo       normalize: false
echo       hidden_units: 128
echo       num_layers: 2
echo       vis_encode_type: simple
echo     reward_signals:
echo       extrinsic:
echo         gamma: 0.99
echo         strength: 1.0
echo     max_steps: 500000
echo     time_horizon: 64
echo     summary_freq: 1000
echo     keep_checkpoints: 5
echo     checkpoint_interval: 50000
echo     threaded: false
) > config\squadmate-ppo.yaml

REM Generate unique run ID with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "run_id=squadmate_%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

echo ✅ Configuration ready!
echo 🎯 Run ID: %run_id%
echo.

REM Start TensorBoard in background
echo 📊 Starting TensorBoard...
start /min cmd /c "tensorboard --logdir results --host localhost --port 6006"

REM Wait for TensorBoard to start
timeout /t 2 /nobreak >nul

REM Open TensorBoard in browser
echo 🌐 Opening TensorBoard in browser...
start http://localhost:6006

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                🎮 UNITY SETUP                                ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. Open Unity Hub                                                           ║
echo ║  2. Open your SquadMate project                                              ║
echo ║  3. Go to: SquadMate AI → 🚀 ONE CLICK TRAINING                              ║
echo ║  4. Click "START ONE CLICK TRAINING" button                                  ║
echo ║  5. Press PLAY in Unity when training starts                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🤖 Starting ML-Agents training...
echo.
echo 📊 TensorBoard: http://localhost:6006
echo 🛑 Press Ctrl+C to stop training
echo ⏱️  Training will run for up to 500,000 steps
echo.

REM Start ML-Agents training
mlagents-learn config\squadmate-ppo.yaml --run-id=%run_id% --force

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            🏁 TRAINING COMPLETED                             ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  📁 Results saved in: results\%run_id%\                                      ║
echo ║  🧠 Model file: results\%run_id%\SquadMate.onnx                             ║
echo ║  📊 TensorBoard logs: results\%run_id%\                                     ║
echo ║                                                                              ║
echo ║  🎯 Next Steps:                                                              ║
echo ║  1. Copy the .onnx file to Assets/Models/ in Unity                          ║
echo ║  2. Set the model in your agent's Behavior Parameters                       ║
echo ║  3. Test your trained AI in play mode!                                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Open results folder
echo 📁 Opening results folder...
start explorer results

pause
