using UnityEngine;

/// <summary>
/// Simple medkit pickup script that matches the interface you showed
/// Works with both InventorySystem and existing HealthSystem
/// </summary>
public class SimpleMedkitPickup : MonoBehaviour
{
    [Header("💊 Healing Settings")]
    public string healingName = "Medkit";
    public float healAmount = 100f;
    public bool instantHeal = true;
    public float useTime = 6f;
    
    [Header("🎨 Visual Settings")]
    public bool enableFloating = true;
    public float floatSpeed = 2f;
    public float floatHeight = 0.15f;
    public float rotationSpeed = 30f;
    
    private Vector3 startPosition;
    private bool isPickedUp = false;
    
    void Start()
    {
        startPosition = transform.position;
        
        // Ensure collider is trigger
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.isTrigger = true;
        }
        
        Debug.Log($"💊 {healingName} pickup ready - Heal: {healAmount}");
    }
    
    void Update()
    {
        if (isPickedUp) return;
        
        // Floating animation
        if (enableFloating)
        {
            float newY = startPosition.y + Mathf.Sin(Time.time * floatSpeed) * floatHeight;
            transform.position = new Vector3(startPosition.x, newY, startPosition.z);
            transform.Rotate(0, rotationSpeed * Time.deltaTime, 0);
        }
    }
    
    private void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;
        
        if (other.CompareTag("Player") || other.CompareTag("Agent"))
        {
            // Check if the character needs healing
            HealthSystem healthSystem = other.GetComponent<HealthSystem>();
            if (healthSystem == null)
            {
                healthSystem = other.gameObject.AddComponent<HealthSystem>();
            }
            
            // Only pickup if health is not full
            if (healthSystem.GetHealthPercent() < 1f)
            {
                var inventory = other.GetComponent<InventorySystem>();
                if (inventory != null)
                {
                    bool success = inventory.AddHealing(healingName);
                    if (success)
                    {
                        // Apply healing effect
                        if (instantHeal)
                        {
                            healthSystem.Heal(healAmount);
                            Debug.Log($"💚 {other.name} instantly healed {healAmount} HP");
                        }
                        else
                        {
                            StartCoroutine(HealOverTime(healthSystem, other.gameObject));
                        }
                        
                        // Reward agent if applicable
                        SquadMateAgent agent = other.GetComponent<SquadMateAgent>();
                        if (agent != null)
                        {
                            float reward = CalculateHealingReward(healthSystem);
                            agent.AddReward(reward);
                            Debug.Log($"🎯 Agent rewarded {reward:F2} for picking up {healingName}");
                        }
                        
                        PickupMedkit(other.gameObject);
                    }
                    else
                    {
                        Debug.Log($"❌ {other.name} cannot pickup {healingName} - inventory full");
                    }
                }
                else
                {
                    Debug.LogWarning($"⚠️ {other.name} has no InventorySystem component");
                }
            }
            else
            {
                Debug.Log($"💚 {other.name} is already at full health");
            }
        }
    }
    
    private float CalculateHealingReward(HealthSystem healthSystem)
    {
        float baseReward = 0.2f;
        float healthPercent = healthSystem.GetHealthPercent();
        
        // Higher reward when health is lower
        if (healthPercent < 0.25f)
        {
            baseReward = 0.5f; // Critical health
        }
        else if (healthPercent < 0.5f)
        {
            baseReward = 0.4f; // Low health
        }
        else if (healthPercent < 0.75f)
        {
            baseReward = 0.3f; // Medium health
        }
        
        // Bonus for high-value healing items
        if (healingName.Contains("Med Kit") || healAmount >= 100f)
        {
            baseReward += 0.1f;
        }
        
        return baseReward;
    }
    
    private System.Collections.IEnumerator HealOverTime(HealthSystem healthSystem, GameObject target)
    {
        Debug.Log($"💊 {target.name} started using {healingName} ({useTime}s)");
        
        float healPerSecond = healAmount / useTime;
        float elapsed = 0f;
        
        while (elapsed < useTime)
        {
            float deltaHeal = healPerSecond * Time.deltaTime;
            healthSystem.Heal(deltaHeal);
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        Debug.Log($"💚 {target.name} finished using {healingName}");
    }
    
    private void PickupMedkit(GameObject picker)
    {
        isPickedUp = true;
        
        Debug.Log($"💊 {picker.name} picked up {healingName}!");
        
        // Visual pickup effect
        StartCoroutine(PickupEffect());
    }
    
    private System.Collections.IEnumerator PickupEffect()
    {
        // Scale up and fade out effect
        Vector3 originalScale = transform.localScale;
        Renderer renderer = GetComponent<Renderer>();
        Color originalColor = Color.white;
        
        if (renderer != null && renderer.material != null)
        {
            originalColor = renderer.material.color;
        }
        
        float timer = 0f;
        float duration = 0.5f;
        
        while (timer < duration)
        {
            timer += Time.deltaTime;
            float progress = timer / duration;
            
            // Scale up
            transform.localScale = Vector3.Lerp(originalScale, originalScale * 1.2f, progress);
            
            // Fade out
            if (renderer != null && renderer.material != null)
            {
                Color newColor = originalColor;
                newColor.a = 1f - progress;
                renderer.material.color = newColor;
            }
            
            yield return null;
        }
        
        // Destroy the pickup
        Destroy(gameObject);
    }
    
    /// <summary>
    /// Set healing properties programmatically
    /// </summary>
    public void SetHealingProperties(string name, float heal, bool instant, float time)
    {
        healingName = name;
        healAmount = heal;
        instantHeal = instant;
        useTime = time;
    }
    
    /// <summary>
    /// Create a medkit pickup from healing data
    /// </summary>
    public static GameObject CreateMedkitPickup(string healingName, Vector3 position, Transform parent = null)
    {
        GameObject pickup = GameObject.CreatePrimitive(PrimitiveType.Cube);
        pickup.name = $"{healingName}_Pickup";
        pickup.transform.position = position;
        pickup.transform.localScale = new Vector3(0.6f, 0.3f, 0.8f);
        
        if (parent != null)
        {
            pickup.transform.SetParent(parent);
        }
        
        // Add the pickup script
        SimpleMedkitPickup medkitPickup = pickup.AddComponent<SimpleMedkitPickup>();
        medkitPickup.healingName = healingName;
        
        // Set healing-specific properties
        switch (healingName.ToLower())
        {
            case "medkit":
            case "med kit":
                medkitPickup.SetHealingProperties("Med Kit", 100f, false, 8f);
                pickup.GetComponent<Renderer>().material.color = Color.cyan;
                break;
            case "firstaid":
            case "first aid":
            case "firstaidkit":
                medkitPickup.SetHealingProperties("First Aid Kit", 75f, false, 6f);
                pickup.GetComponent<Renderer>().material.color = Color.green;
                break;
            case "bandage":
                medkitPickup.SetHealingProperties("Bandage", 10f, false, 4f);
                pickup.GetComponent<Renderer>().material.color = Color.white;
                break;
            case "energydrink":
            case "energy drink":
                medkitPickup.SetHealingProperties("Energy Drink", 40f, true, 4f);
                pickup.GetComponent<Renderer>().material.color = Color.yellow;
                break;
            default:
                medkitPickup.SetHealingProperties(healingName, 50f, true, 5f);
                pickup.GetComponent<Renderer>().material.color = Color.green;
                break;
        }
        
        // Ensure collider is trigger
        pickup.GetComponent<Collider>().isTrigger = true;
        
        // Add tag
        pickup.tag = "Medkit";
        
        Debug.Log($"✅ Created {healingName} pickup at {position}");
        return pickup;
    }
}
