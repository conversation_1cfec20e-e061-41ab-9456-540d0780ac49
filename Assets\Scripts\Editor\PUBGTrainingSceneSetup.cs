using UnityEngine;
using UnityEditor;
using Unity.MLAgents;
using UnityEngine.AI;
using UnityEditor.SceneManagement;

/// <summary>
/// Advanced PUBG-style training scene setup with all systems integrated
/// </summary>
public class PUBGTrainingSceneSetup : EditorWindow
{
    [MenuItem("SquadMate AI/🎯 Create PUBG Training Scene")]
    public static void ShowWindow()
    {
        GetWindow<PUBGTrainingSceneSetup>("PUBG Training Setup");
    }

    void OnGUI()
    {
        GUILayout.Label("🎮 PUBG-Style Training Scene Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This will create a complete PUBG training environment:");
        GUILayout.Label("🏗️ Large terrain with buildings and cover");
        GUILayout.Label("📦 Advanced loot zones (Military, Medical, Residential)");
        GUILayout.Label("🔫 Weapon attachments and armor system");
        GUILayout.Label("🤖 Smart enemy AI with tactical behavior");
        GUILayout.Label("🧠 ML-Agents optimized for PUBG training");
        GUILayout.Label("⚙️ Dynamic reward system");
        GUILayout.Label("🎮 One-click training ready");

        GUILayout.Space(20);

        if (GUILayout.Button("🚀 CREATE PUBG TRAINING SCENE", GUILayout.Height(50)))
        {
            CreatePUBGTrainingScene();
        }

        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This will create a comprehensive PUBG-style training environment. Make sure to save any important work first!", MessageType.Warning);
    }

    public static void CreatePUBGTrainingScene()
    {
        Debug.Log("🚀 Creating PUBG Training Scene...");
        Debug.Log("This will create a complete PUBG-style training environment!");

        try
        {
            // Step 1: Clear and prepare scene
            ClearCurrentScene();

            // Step 2: Create PUBG materials and textures
            CreatePUBGMaterials();

            // Step 3: Create large PUBG-style terrain
            GameObject terrain = CreatePUBGTerrain();

            // Step 4: Create buildings and cover structures
            CreatePUBGBuildings(terrain);

            // Step 5: Setup realistic lighting
            SetupPUBGLighting();

            // Step 6: Create player with all PUBG components
            GameObject player = CreatePUBGPlayer();

            // Step 7: Create squadmate with advanced ML-Agents
            GameObject squadmate = CreatePUBGSquadMate();

            // Step 8: Create advanced environment with all systems
            GameObject environment = CreatePUBGEnvironment();

            // Step 9: Create loot zone system
            CreatePUBGLootZones(environment);

            // Step 10: Create enemy spawn system
            CreatePUBGEnemySystem(environment);

            // Step 11: Create all PUBG prefabs
            CreateAllPUBGPrefabs();

            // Step 12: Configure all references and systems
            ConfigurePUBGReferences(player, squadmate, environment);

            // Step 13: Setup advanced NavMesh
            SetupPUBGNavMesh();

            // Step 14: Configure ML-Agents training
            ConfigureMLAgentsTraining(squadmate);

            // Step 15: Save scene
            SavePUBGTrainingScene();

            Debug.Log("🎉 PUBG TRAINING SCENE CREATED SUCCESSFULLY!");
            Debug.Log("✅ All PUBG systems configured and ready!");
            Debug.Log("🎮 Press Play in Unity to start PUBG-style training!");

            // Show success dialog
            EditorUtility.DisplayDialog("PUBG Training Scene Ready!",
                "Complete PUBG SquadMate training scene created successfully!\n\n" +
                "✅ Advanced loot zones configured\n" +
                "✅ Weapon attachments system ready\n" +
                "✅ Armor and tactical systems active\n" +
                "✅ ML-Agents optimized for PUBG training\n" +
                "✅ One-click training ready\n\n" +
                "Press Play to start PUBG-style training!", "Start Training!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error creating PUBG training scene: {e.Message}");
            EditorUtility.DisplayDialog("Error", $"Failed to create PUBG training scene:\n{e.Message}", "OK");
        }
    }

    private static void ClearCurrentScene()
    {
        Debug.Log("🧹 Clearing current scene...");

        // Keep only the main camera
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name != "Main Camera" && obj.transform.parent == null)
            {
                DestroyImmediate(obj);
            }
        }
    }

    private static void CreatePUBGMaterials()
    {
        Debug.Log("🎨 Creating PUBG materials...");

        if (!AssetDatabase.IsValidFolder("Assets/Materials"))
            AssetDatabase.CreateFolder("Assets", "Materials");

        if (!AssetDatabase.IsValidFolder("Assets/Materials/PUBG"))
            AssetDatabase.CreateFolder("Assets/Materials", "PUBG");

        // Player and agent materials
        CreateMaterial("PUBG/PlayerMaterial", new Color(0.2f, 0.5f, 1f)); // Blue
        CreateMaterial("PUBG/SquadMateMaterial", new Color(0.2f, 1f, 0.2f)); // Green
        CreateMaterial("PUBG/EnemyMaterial", new Color(1f, 0.2f, 0.2f)); // Red

        // Loot materials
        CreateMaterial("PUBG/WeaponMaterial", new Color(0.8f, 0.8f, 0.2f)); // Yellow
        CreateMaterial("PUBG/MedkitMaterial", new Color(0.2f, 1f, 1f)); // Cyan
        CreateMaterial("PUBG/ArmorMaterial", new Color(0.5f, 0.5f, 0.5f)); // Gray
        CreateMaterial("PUBG/AttachmentMaterial", new Color(1f, 0.5f, 0.2f)); // Orange

        // Environment materials
        CreateMaterial("PUBG/BuildingMaterial", new Color(0.6f, 0.6f, 0.6f)); // Light Gray
        CreateMaterial("PUBG/CoverMaterial", new Color(0.4f, 0.3f, 0.2f)); // Brown
        CreateMaterial("PUBG/LootZoneMaterial", new Color(1f, 1f, 0.2f, 0.3f)); // Transparent Yellow

        AssetDatabase.SaveAssets();
    }

    private static void CreateMaterial(string name, Color color)
    {
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        mat.color = color;
        if (color.a < 1f)
        {
            // Make transparent
            mat.SetFloat("_Surface", 1); // Transparent
            mat.SetFloat("_Blend", 0); // Alpha
        }
        AssetDatabase.CreateAsset(mat, $"Assets/Materials/{name}.mat");
    }

    private static GameObject CreatePUBGTerrain()
    {
        Debug.Log("🌍 Creating PUBG terrain...");

        TerrainData terrainData = new TerrainData();
        terrainData.heightmapResolution = 1025; // Higher resolution for PUBG
        terrainData.size = new Vector3(200, 20, 200); // Larger terrain

        // Create varied terrain with hills and valleys
        float[,] heights = new float[1025, 1025];
        for (int x = 0; x < 1025; x++)
        {
            for (int y = 0; y < 1025; y++)
            {
                float height = 0f;

                // Large hills
                height += Mathf.PerlinNoise(x * 0.005f, y * 0.005f) * 0.3f;

                // Medium variation
                height += Mathf.PerlinNoise(x * 0.02f, y * 0.02f) * 0.1f;

                // Small details
                height += Mathf.PerlinNoise(x * 0.1f, y * 0.1f) * 0.02f;

                heights[x, y] = height;
            }
        }
        terrainData.SetHeights(0, 0, heights);

        if (!AssetDatabase.IsValidFolder("Assets/Terrain"))
            AssetDatabase.CreateFolder("Assets", "Terrain");
        AssetDatabase.CreateAsset(terrainData, "Assets/Terrain/PUBGTerrain.asset");

        GameObject terrainObj = Terrain.CreateTerrainGameObject(terrainData);
        terrainObj.name = "PUBG Terrain";
        GameObjectUtility.SetStaticEditorFlags(terrainObj, StaticEditorFlags.NavigationStatic);

        return terrainObj;
    }

    private static void CreatePUBGBuildings(GameObject terrain)
    {
        Debug.Log("🏢 Creating PUBG buildings and cover...");

        GameObject buildingsParent = new GameObject("Buildings");

        // Create various building types
        Vector3[] buildingPositions = new Vector3[]
        {
            new Vector3(50, 0, 50), new Vector3(-50, 0, 50), new Vector3(50, 0, -50), new Vector3(-50, 0, -50),
            new Vector3(80, 0, 20), new Vector3(-80, 0, -20), new Vector3(20, 0, 80), new Vector3(-20, 0, -80),
            new Vector3(0, 0, 70), new Vector3(70, 0, 0), new Vector3(-70, 0, 0), new Vector3(0, 0, -70)
        };

        Material buildingMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/BuildingMaterial.mat");
        Material coverMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/CoverMaterial.mat");

        foreach (Vector3 pos in buildingPositions)
        {
            CreateBuilding(pos, buildingsParent.transform, buildingMat, coverMat);
        }
    }

    private static void CreateBuilding(Vector3 position, Transform parent, Material buildingMat, Material coverMat)
    {
        // Main building
        GameObject building = GameObject.CreatePrimitive(PrimitiveType.Cube);
        building.name = "Building";
        building.transform.position = position + Vector3.up * 2.5f;
        building.transform.localScale = new Vector3(8, 5, 8);
        building.transform.SetParent(parent);

        if (buildingMat != null)
            building.GetComponent<Renderer>().sharedMaterial = buildingMat;

        GameObjectUtility.SetStaticEditorFlags(building, StaticEditorFlags.NavigationStatic);

        // Add cover objects around building
        Vector3[] coverPositions = new Vector3[]
        {
            position + new Vector3(6, 0.5f, 0), position + new Vector3(-6, 0.5f, 0),
            position + new Vector3(0, 0.5f, 6), position + new Vector3(0, 0.5f, -6)
        };

        foreach (Vector3 coverPos in coverPositions)
        {
            GameObject cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cover.name = "Cover";
            cover.transform.position = coverPos;
            cover.transform.localScale = new Vector3(2, 1, 1);
            cover.transform.SetParent(parent);

            if (coverMat != null)
                cover.GetComponent<Renderer>().sharedMaterial = coverMat;

            GameObjectUtility.SetStaticEditorFlags(cover, StaticEditorFlags.NavigationStatic);
        }
    }

    private static void SetupPUBGLighting()
    {
        Debug.Log("💡 Setting up PUBG lighting...");

        GameObject lightObj = new GameObject("Directional Light");
        Light light = lightObj.AddComponent<Light>();
        light.type = LightType.Directional;
        light.intensity = 2f;
        light.shadows = LightShadows.Soft;
        light.shadowStrength = 0.8f;
        lightObj.transform.rotation = Quaternion.Euler(30f, 45f, 0f);

        // PUBG-style lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.6f, 0.8f, 1f);
        RenderSettings.ambientEquatorColor = new Color(0.5f, 0.5f, 0.5f);
        RenderSettings.ambientGroundColor = new Color(0.3f, 0.3f, 0.3f);
        RenderSettings.fog = true;
        RenderSettings.fogColor = new Color(0.7f, 0.8f, 0.9f);
        RenderSettings.fogMode = FogMode.ExponentialSquared;
        RenderSettings.fogDensity = 0.001f;
    }

    private static GameObject CreatePUBGPlayer()
    {
        Debug.Log("👤 Creating PUBG player...");

        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        player.transform.localScale = new Vector3(1, 2, 1);

        // Add Rigidbody
        Rigidbody rb = player.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Add PUBG components
        player.AddComponent<PlayerController>();
        player.AddComponent<InventorySystem>();
        player.AddComponent<HealthSystem>();
        player.AddComponent<PUBGInventory>();
        player.AddComponent<PUBGArmorSystem>();

        // Apply material
        Material playerMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/PlayerMaterial.mat");
        if (playerMat != null)
            player.GetComponent<Renderer>().sharedMaterial = playerMat;

        return player;
    }

    private static GameObject CreatePUBGSquadMate()
    {
        Debug.Log("🤖 Creating PUBG SquadMate with advanced ML-Agents...");

        GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        squadmate.name = "SquadMate";
        squadmate.transform.position = new Vector3(3, 1, 0);
        squadmate.transform.localScale = new Vector3(1, 2, 1);

        // Add Rigidbody
        Rigidbody rb = squadmate.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Add ML-Agents components
        BehaviorParameters behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;

        // Add SquadMate scripts
        squadmate.AddComponent<SquadMateAgent>();
        squadmate.AddComponent<RewardCalculator>();
        squadmate.AddComponent<SquadMateDecisionTree>();
        squadmate.AddComponent<DynamicRewardSystem>();

        // Add PUBG systems
        squadmate.AddComponent<InventorySystem>();
        squadmate.AddComponent<HealthSystem>();
        squadmate.AddComponent<PUBGInventory>();
        squadmate.AddComponent<PUBGArmorSystem>();
        squadmate.AddComponent<WeaponSystem>();

        // Apply material
        Material squadmateMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PUBG/SquadMateMaterial.mat");
        if (squadmateMat != null)
            squadmate.GetComponent<Renderer>().sharedMaterial = squadmateMat;

        return squadmate;
    }

    private static GameObject CreatePUBGEnvironment()
    {
        Debug.Log("🌍 Creating PUBG environment with all systems...");

        GameObject environment = new GameObject("PUBG Environment");
        environment.transform.position = Vector3.zero;

        // Add environment scripts
        environment.AddComponent<GameEnvironment>();
        environment.AddComponent<ObjectSpawner>();
        environment.AddComponent<PUBGItemSystem>();
        environment.AddComponent<LootZoneManager>();
        environment.AddComponent<CombatTrainingEnvironment>();
        environment.AddComponent<SimpleTrainingEnvironment>();

        return environment;
    }

    private static void CreatePUBGLootZones(GameObject environment)
    {
        PUBGTrainingSceneSetupPart2.CreatePUBGLootZones(environment);
    }

    private static void CreatePUBGEnemySystem(GameObject environment)
    {
        PUBGTrainingSceneSetupPart2.CreatePUBGEnemySystem(environment);
    }

    private static void CreateAllPUBGPrefabs()
    {
        PUBGTrainingSceneSetupPart2.CreateAllPUBGPrefabs();
    }

    private static void ConfigurePUBGReferences(GameObject player, GameObject squadmate, GameObject environment)
    {
        Debug.Log("🔗 Configuring PUBG references...");

        // Configure SquadMate references
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.player = player.transform;
            agent.environment = environment.GetComponent<GameEnvironment>();
        }

        // Configure Environment references
        GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
        if (gameEnv != null)
        {
            gameEnv.player = player.transform;
            gameEnv.squadMate = agent;

            // Assign PUBG prefabs
            gameEnv.enemyPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/PUBGEnemy.prefab");
            gameEnv.medkitPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/MedKit.prefab");
            gameEnv.weaponPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/M416.prefab");

            // Configure PUBG weapon prefabs array
            gameEnv.weaponPrefabs = new GameObject[]
            {
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/M416.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/UMP45.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Kar98k.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/SCAR-L.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/AKM.prefab")
            };

            // Configure PUBG healing prefabs array
            gameEnv.healingPrefabs = new GameObject[]
            {
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/MedKit.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/FirstAidKit.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Bandage.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/EnergyDrink.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Painkiller.prefab")
            };

            // Configure PUBG armor prefabs array
            gameEnv.armorPrefabs = new GameObject[]
            {
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Level3Helmet.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Level3Vest.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Level2Helmet.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Level2Vest.prefab")
            };

            // Configure PUBG attachment prefabs array
            gameEnv.attachmentPrefabs = new GameObject[]
            {
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/4xScope.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/RedDotSight.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Compensator.prefab"),
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/PUBG/Suppressor.prefab")
            };

            // Configure environment settings for PUBG
            gameEnv.environmentSize = new Vector3(200f, 0f, 200f);
            gameEnv.maxEnemies = 8;
            gameEnv.maxMedkits = 6;
            gameEnv.maxWeapons = 10;
        }

        // Configure LootZoneManager
        LootZoneManager lootManager = environment.GetComponent<LootZoneManager>();
        if (lootManager != null)
        {
            lootManager.weaponPrefabs = gameEnv.weaponPrefabs;
            lootManager.healingPrefabs = gameEnv.healingPrefabs;
            lootManager.armorPrefabs = gameEnv.armorPrefabs;
            lootManager.attachmentPrefabs = gameEnv.attachmentPrefabs;
        }

        // Configure PUBGItemSystem
        PUBGItemSystem itemSystem = environment.GetComponent<PUBGItemSystem>();
        if (itemSystem != null)
        {
            // This will be auto-configured when the scene starts
        }
    }

    private static void SetupPUBGNavMesh()
    {
        Debug.Log("🗺️ Setting up PUBG NavMesh system...");

        try
        {
            // Configure NavMesh settings for PUBG
            var navMeshData = NavMeshBuilder.BuildNavMeshData(
                new NavMeshBuildSettings
                {
                    agentTypeID = 0,
                    agentRadius = 0.5f,
                    agentHeight = 2f,
                    agentSlope = 45f,
                    agentClimb = 0.4f,
                    minRegionArea = 2f,
                    overrideVoxelSize = false,
                    voxelSize = 0.16666667f,
                    overrideTileSize = false,
                    tileSize = 256
                },
                new List<NavMeshBuildSource>(),
                new Bounds(Vector3.zero, new Vector3(200f, 20f, 200f))
            );

            NavMesh.AddNavMeshData(navMeshData);
            Debug.Log("✅ PUBG NavMesh built successfully");
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"NavMesh baking failed: {e.Message}. You can bake manually later.");
        }
    }

    private static void ConfigureMLAgentsTraining(GameObject squadmate)
    {
        Debug.Log("🧠 Configuring ML-Agents for PUBG training...");

        // Configure BehaviorParameters for PUBG training
        BehaviorParameters behaviorParams = squadmate.GetComponent<BehaviorParameters>();
        if (behaviorParams != null)
        {
            behaviorParams.BehaviorName = "SquadMate";
            behaviorParams.BehaviorType = BehaviorType.Default;
            behaviorParams.TeamId = 0;
            behaviorParams.UseChildSensors = true;
        }

        // Configure SquadMateAgent for PUBG
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            // These will be configured at runtime
            Debug.Log("✅ SquadMateAgent configured for PUBG training");
        }

        // Configure DynamicRewardSystem
        DynamicRewardSystem rewardSystem = squadmate.GetComponent<DynamicRewardSystem>();
        if (rewardSystem != null)
        {
            // This will auto-configure based on PUBG systems
            Debug.Log("✅ Dynamic reward system configured for PUBG");
        }
    }

    private static void SavePUBGTrainingScene()
    {
        Debug.Log("💾 Saving PUBG training scene...");

        if (!AssetDatabase.IsValidFolder("Assets/Scenes"))
            AssetDatabase.CreateFolder("Assets", "Scenes");

        if (!AssetDatabase.IsValidFolder("Assets/Scenes/PUBG"))
            AssetDatabase.CreateFolder("Assets/Scenes", "PUBG");

        UnityEditor.SceneManagement.EditorSceneManager.SaveScene(
            UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene(),
            "Assets/Scenes/PUBG/PUBGTrainingEnvironment.unity"
        );

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("✅ PUBG training scene saved successfully!");
    }
}
