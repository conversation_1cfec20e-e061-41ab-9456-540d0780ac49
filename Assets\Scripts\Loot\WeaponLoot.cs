using UnityEngine;

/// <summary>
/// Weapon loot items (M416, UMP45, Kar98k, etc.)
/// </summary>
public class WeaponLoot : LootItem
{
    [Header("🔫 Weapon Stats")]
    public WeaponClass weaponClass = WeaponClass.AssaultRifle;
    public float damage = 35f;
    public float range = 400f;
    public float fireRate = 0.086f; // Time between shots
    public int magazineSize = 30;
    public float reloadTime = 2.3f;
    public AmmoType ammoType = AmmoType.Ammo556;
    
    [Header("🎯 Weapon Properties")]
    public float accuracy = 0.85f; // 0-1 scale
    public float stability = 0.8f; // Recoil control
    public bool isFullAuto = true;
    public bool canAttachScope = true;
    public bool canAttachGrip = true;
    public bool canAttachMuzzle = true;
    
    [Header("🏆 PUBG Weapon Tier")]
    public WeaponTier weaponTier = WeaponTier.Tier2;

    public enum WeaponClass
    {
        AssaultRifle,
        SMG,
        SniperR<PERSON>le,
        Shotgun,
        Pistol,
        LMG,
        DMR
    }

    public enum AmmoType
    {
        Ammo556,
        Ammo762,
        Ammo9mm,
        Ammo45ACP,
        Ammo12Gauge,
        Ammo300Magnum
    }

    public enum WeaponTier
    {
        Tier1, // Basic weapons (UMP, Vector)
        Tier2, // Good weapons (M416, SCAR-L)
        Tier3, // Best weapons (AKM, M762)
        Crate  // Crate weapons (AWM, Groza)
    }

    protected override void Start()
    {
        base.Start();
        lootType = LootType.Weapon;
        
        // Set weapon-specific values
        SetWeaponValues();
    }

    void SetWeaponValues()
    {
        // Set reward value based on weapon tier and class
        switch (weaponTier)
        {
            case WeaponTier.Tier1:
                rewardValue = 0.1f;
                itemValue = 1f;
                break;
            case WeaponTier.Tier2:
                rewardValue = 0.2f;
                itemValue = 2f;
                break;
            case WeaponTier.Tier3:
                rewardValue = 0.3f;
                itemValue = 3f;
                break;
            case WeaponTier.Crate:
                rewardValue = 0.5f;
                itemValue = 5f;
                rarity = LootRarity.Legendary;
                break;
        }

        // Bonus for assault rifles (most versatile)
        if (weaponClass == WeaponClass.AssaultRifle)
        {
            rewardValue += 0.05f;
            itemValue += 0.5f;
        }
    }

    protected override void ApplyItemEffects(GameObject picker)
    {
        // Give weapon to picker
        WeaponSystem weaponSystem = picker.GetComponent<WeaponSystem>();
        if (weaponSystem == null)
        {
            weaponSystem = picker.AddComponent<WeaponSystem>();
        }

        // Equip the weapon
        weaponSystem.EquipWeapon(itemName, damage, range, fireRate, magazineSize);

        // Update agent weapon status
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.hasWeapon = true;
            
            // Extra reward for upgrading to better weapon
            if (IsWeaponUpgrade(agent))
            {
                agent.AddReward(0.1f);
                Debug.Log($"🔫 Weapon upgrade bonus for {itemName}!");
            }
        }

        // Player weapon handling
        PlayerController player = picker.GetComponent<PlayerController>();
        if (player != null)
        {
            // Handle player weapon pickup (implement as needed)
            Debug.Log($"🔫 Player equipped {itemName}");
        }
    }

    bool IsWeaponUpgrade(SquadMateAgent agent)
    {
        // Check if this weapon is better than current weapon
        WeaponSystem currentWeapon = agent.GetComponent<WeaponSystem>();
        if (currentWeapon == null || !currentWeapon.HasWeapon())
        {
            return true; // Any weapon is better than no weapon
        }

        // Simple upgrade logic - higher tier = better
        // In a real implementation, you'd compare actual weapon stats
        return weaponTier >= WeaponTier.Tier2;
    }

    public override float GetAIPriority(SquadMateAgent agent)
    {
        float priority = base.GetAIPriority(agent);
        
        // Higher priority if agent doesn't have a weapon
        WeaponSystem weaponSystem = agent.GetComponent<WeaponSystem>();
        if (weaponSystem == null || !weaponSystem.HasWeapon())
        {
            priority *= 2f;
        }
        
        // Higher priority for assault rifles
        if (weaponClass == WeaponClass.AssaultRifle)
        {
            priority *= 1.3f;
        }
        
        // Higher priority for better tiers
        priority *= (float)weaponTier + 1f;
        
        return priority;
    }

    protected override void SetRarityColor()
    {
        if (itemRenderer == null) return;
        
        // Set color based on weapon tier
        Color weaponColor;
        switch (weaponTier)
        {
            case WeaponTier.Tier1:
                weaponColor = Color.gray;
                break;
            case WeaponTier.Tier2:
                weaponColor = Color.blue;
                break;
            case WeaponTier.Tier3:
                weaponColor = Color.magenta;
                break;
            case WeaponTier.Crate:
                weaponColor = Color.yellow;
                break;
            default:
                weaponColor = Color.white;
                break;
        }

        itemRenderer.material.color = weaponColor;
    }

    // Static factory methods for creating specific weapons
    public static WeaponLoot CreateM416(GameObject prefab)
    {
        WeaponLoot weapon = prefab.GetComponent<WeaponLoot>();
        weapon.itemName = "M416";
        weapon.weaponClass = WeaponClass.AssaultRifle;
        weapon.weaponTier = WeaponTier.Tier2;
        weapon.damage = 41f;
        weapon.range = 500f;
        weapon.fireRate = 0.086f;
        weapon.magazineSize = 30;
        weapon.accuracy = 0.85f;
        weapon.stability = 0.8f;
        weapon.ammoType = AmmoType.Ammo556;
        weapon.rarity = LootRarity.Rare;
        return weapon;
    }

    public static WeaponLoot CreateUMP45(GameObject prefab)
    {
        WeaponLoot weapon = prefab.GetComponent<WeaponLoot>();
        weapon.itemName = "UMP45";
        weapon.weaponClass = WeaponClass.SMG;
        weapon.weaponTier = WeaponTier.Tier1;
        weapon.damage = 35f;
        weapon.range = 200f;
        weapon.fireRate = 0.092f;
        weapon.magazineSize = 25;
        weapon.accuracy = 0.75f;
        weapon.stability = 0.9f;
        weapon.ammoType = AmmoType.Ammo45ACP;
        weapon.rarity = LootRarity.Common;
        return weapon;
    }

    public static WeaponLoot CreateKar98k(GameObject prefab)
    {
        WeaponLoot weapon = prefab.GetComponent<WeaponLoot>();
        weapon.itemName = "Kar98k";
        weapon.weaponClass = WeaponClass.SniperRifle;
        weapon.weaponTier = WeaponTier.Tier3;
        weapon.damage = 75f;
        weapon.range = 1000f;
        weapon.fireRate = 1.8f; // Bolt action
        weapon.magazineSize = 5;
        weapon.accuracy = 0.95f;
        weapon.stability = 0.6f;
        weapon.ammoType = AmmoType.Ammo762;
        weapon.isFullAuto = false;
        weapon.rarity = LootRarity.Epic;
        return weapon;
    }
}
