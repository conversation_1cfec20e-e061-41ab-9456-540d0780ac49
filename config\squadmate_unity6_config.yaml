behaviors:
  SquadMate:
    trainer_type: ppo
    hyperparameters:
      batch_size: 4096 # Optimized for PUBG tactical training
      buffer_size: 40960 # Large buffer for complex PUBG scenarios
      learning_rate: 2.5e-4 # Slightly lower for stable PUBG learning
      beta: 3.0e-3 # Reduced for better exploration in PUBG
      epsilon: 0.15 # Tighter for tactical precision
      lambd: 0.97 # Higher for long-term PUBG strategy
      num_epoch: 4 # More epochs for complex PUBG decisions
      learning_rate_schedule: linear

    network_settings:
      normalize: true # Enable for PUBG observation normalization
      hidden_units: 768 # Larger network for PUBG complexity
      num_layers: 4 # Deeper network for tactical decisions
      vis_encode_type: simple
      memory:
        sequence_length: 128 # Extended memory for PUBG tactical sequences
        memory_size: 512 # Large memory for complex PUBG scenarios

    reward_signals:
      extrinsic:
        gamma: 0.995 # Higher gamma for long-term PUBG strategy
        strength: 1.0
      curiosity:
        gamma: 0.99
        strength: 0.015 # Reduced for focused PUBG learning
        encoding_size: 512 # Larger encoding for PUBG complexity
        learning_rate: 8e-4
      gail: # Advanced imitation learning for PUBG tactics
        gamma: 0.995
        strength: 0.008
        demo_path: demonstrations/pubg_squadmate_demos.demo

    max_steps: 8000000 # Extended for comprehensive PUBG training
    time_horizon: 512 # Much longer horizon for PUBG tactical planning
    summary_freq: 2500 # More frequent summaries for PUBG monitoring
    threaded: true # Enable threading for Unity 6 performance

    self_play:
      save_steps: 20000 # More frequent saves for PUBG training
      team_change: 40000 # Faster team changes for varied PUBG scenarios
      swap_steps: 4000 # Quicker swaps for dynamic PUBG training
      play_against_latest_model_ratio: 0.6 # Higher ratio for competitive PUBG training
      window: 15 # Smaller window for faster PUBG adaptation

    behavioral_cloning: # Enhanced BC for PUBG tactics
      demo_path: demonstrations/pubg_squadmate_demos.demo
      strength: 0.3 # Reduced for more exploration in PUBG
      steps: 75000 # More BC steps for complex PUBG behaviors

env_settings:
  env_path: null
  env_args: null
  base_port: 5005
  num_envs: 1
  num_areas: 8 # More training areas for diverse PUBG scenarios
  seed: -1
  max_lifetime_restarts: 15 # More restarts for robust PUBG training
  restarts_rate_limit_n: 2
  restarts_rate_limit_period_s: 45

engine_settings:
  width: 1920 # High resolution for detailed PUBG training
  height: 1080
  quality_level: 1 # Slightly higher quality for PUBG visual training
  time_scale: 20 # Higher time scale for faster PUBG training
  target_frame_rate: 60
  capture_frame_rate: 60
  no_graphics: false # Keep graphics for PUBG visual learning

checkpoint_settings:
  run_id: pubg_squadmate_training
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  results_dir: results
  keep_checkpoints: 10 # Keep more checkpoints in Unity 6

# Unity 6 specific optimizations
unity6_optimizations:
  use_burst_compilation: true
  enable_job_system: true
  batch_observations: true
  parallel_environments: true
  memory_optimization: true

# Advanced PUBG training features for Unity 6
advanced_features:
  curriculum_learning:
    enabled: true
    thresholds:
      basic_movement: 0.3 # Lower threshold for faster progression
      loot_collection: 0.6 # Learn looting before combat
      weapon_usage: 1.0 # Master weapons before tactics
      healing_strategy: 1.2 # Learn when to heal
      combat_engagement: 1.5 # Engage enemies effectively
      tactical_positioning: 2.0 # Advanced positioning
      team_coordination: 2.5 # Ultimate PUBG teamwork

  multi_agent_training:
    enabled: true
    team_size: 2 # Player + SquadMate
    opponent_teams: 2 # Multiple enemy teams for PUBG realism
    competitive_training: true # Enable competitive PUBG scenarios

  dynamic_difficulty:
    enabled: true
    adjustment_frequency: 8000 # More frequent adjustments for PUBG
    difficulty_range: [0.3, 3.0] # Wider range for varied PUBG challenges
    enemy_scaling: true # Scale enemy difficulty
    loot_scarcity: true # Adjust loot availability
    zone_pressure: true # Simulate PUBG zone mechanics

  pubg_specific_features:
    loot_priority_training: true # Train loot prioritization
    tactical_retreat_training: true # Learn when to retreat
    healing_timing_training: true # Optimize healing decisions
    weapon_upgrade_training: true # Learn weapon tier preferences
    armor_importance_training: true # Understand armor value
    attachment_optimization: true # Learn attachment benefits
