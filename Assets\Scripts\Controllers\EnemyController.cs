using UnityEngine;
using UnityEngine.AI;

public class EnemyController : MonoBeh<PERSON>our
{
    [Header("Enemy Settings")]
    public float maxHealth = 75f;
    public float moveSpeed = 3f;
    public float attackRange = 8f;
    public float detectionRange = 15f;
    public float attackDamage = 20f;
    public float attackCooldown = 2f;

    [Header("AI Behavior")]
    public float patrolRadius = 10f;
    public float decisionInterval = 1f;

    [Header("Enemy State")]
    public float currentHealth;
    public EnemyState currentState;

    public enum EnemyState
    {
        Patrolling,
        Chasing,
        Attacking,
        Searching
    }

    [Header("References")]
    public GameEnvironment environment;

    private NavMeshAgent navAgent;
    private Transform target;
    private Vector3 patrolCenter;
    private Vector3 patrolTarget;
    private float lastAttackTime;
    private float lastDecisionTime;
    private bool isDead = false;

    void Start()
    {
        navAgent = GetComponent<NavMeshAgent>();
        if (navAgent == null)
        {
            navAgent = gameObject.AddComponent<NavMeshAgent>();
        }

        navAgent.speed = moveSpeed;
        navAgent.stoppingDistance = attackRange * 0.8f;

        currentHealth = maxHealth;
        patrolCenter = transform.position;
        currentState = EnemyState.Patrolling;

        if (environment == null)
        {
            environment = FindObjectOfType<GameEnvironment>();
        }

        // Delay initial patrol target setting to ensure NavMesh is ready
        Invoke(nameof(DelayedStart), 0.5f);
    }

    private void DelayedStart()
    {
        SetNewPatrolTarget();
    }

    void Update()
    {
        if (isDead) return;

        // Make decisions at intervals
        if (Time.time - lastDecisionTime > decisionInterval)
        {
            MakeDecision();
            lastDecisionTime = Time.time;
        }

        // Execute current state behavior
        ExecuteState();

        // Check if dead
        if (currentHealth <= 0 && !isDead)
        {
            Die();
        }
    }

    private void MakeDecision()
    {
        // Find nearest target (player or squadmate)
        Transform nearestTarget = FindNearestTarget();

        if (nearestTarget != null)
        {
            float distanceToTarget = Vector3.Distance(transform.position, nearestTarget.position);

            // Check if target is in detection range
            if (distanceToTarget <= detectionRange)
            {
                target = nearestTarget;

                // Decide state based on distance
                if (distanceToTarget <= attackRange)
                {
                    currentState = EnemyState.Attacking;
                }
                else
                {
                    currentState = EnemyState.Chasing;
                }
            }
            else if (target != null)
            {
                // Lost target, search for a bit
                currentState = EnemyState.Searching;
            }
            else
            {
                currentState = EnemyState.Patrolling;
            }
        }
        else
        {
            currentState = EnemyState.Patrolling;
            target = null;
        }
    }

    private void ExecuteState()
    {
        switch (currentState)
        {
            case EnemyState.Patrolling:
                Patrol();
                break;
            case EnemyState.Chasing:
                ChaseTarget();
                break;
            case EnemyState.Attacking:
                AttackTarget();
                break;
            case EnemyState.Searching:
                SearchForTarget();
                break;
        }
    }

    private void Patrol()
    {
        if (IsNavMeshAgentReady() && navAgent.remainingDistance < 1f)
        {
            SetNewPatrolTarget();
        }
    }

    private void ChaseTarget()
    {
        if (target != null && IsNavMeshAgentReady())
        {
            navAgent.SetDestination(target.position);
        }
    }

    private void AttackTarget()
    {
        if (target == null) return;

        // Stop moving and face target
        if (IsNavMeshAgentReady())
        {
            navAgent.SetDestination(transform.position);
        }

        Vector3 directionToTarget = (target.position - transform.position).normalized;
        Quaternion lookRotation = Quaternion.LookRotation(directionToTarget);
        transform.rotation = Quaternion.Slerp(transform.rotation, lookRotation, Time.deltaTime * 5f);

        // Attack if cooldown is over
        if (Time.time - lastAttackTime > attackCooldown)
        {
            PerformAttack();
            lastAttackTime = Time.time;
        }
    }

    private void SearchForTarget()
    {
        // Move to last known position of target
        if (target != null && IsNavMeshAgentReady())
        {
            navAgent.SetDestination(target.position);

            // If we've been searching for too long, go back to patrolling
            if (Vector3.Distance(transform.position, target.position) < 2f)
            {
                currentState = EnemyState.Patrolling;
                target = null;
            }
        }
        else
        {
            currentState = EnemyState.Patrolling;
        }
    }

    private void SetNewPatrolTarget()
    {
        if (!IsNavMeshAgentReady()) return;

        Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
        randomDirection.y = 0;
        patrolTarget = patrolCenter + randomDirection;

        // Make sure the target is on the NavMesh
        NavMeshHit hit;
        if (NavMesh.SamplePosition(patrolTarget, out hit, patrolRadius, NavMesh.AllAreas))
        {
            patrolTarget = hit.position;
        }

        navAgent.SetDestination(patrolTarget);
    }

    private Transform FindNearestTarget()
    {
        Transform nearestTarget = null;
        float nearestDistance = float.MaxValue;

        // Check player
        if (environment != null && environment.player != null)
        {
            PlayerController playerController = environment.player.GetComponent<PlayerController>();
            if (playerController != null && !playerController.isDowned)
            {
                float distance = Vector3.Distance(transform.position, environment.player.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = environment.player;
                }
            }
        }

        // Check squadmate
        if (environment != null && environment.squadMate != null)
        {
            float distance = Vector3.Distance(transform.position, environment.squadMate.transform.position);
            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearestTarget = environment.squadMate.transform;
            }
        }

        return nearestTarget;
    }

    /// <summary>
    /// Check if NavMeshAgent is ready for navigation
    /// </summary>
    private bool IsNavMeshAgentReady()
    {
        if (navAgent == null) return false;
        if (!navAgent.enabled) return false;
        if (!navAgent.isOnNavMesh) return false;
        if (navAgent.pathPending) return false;

        return true;
    }

    private void PerformAttack()
    {
        if (target == null) return;

        float distanceToTarget = Vector3.Distance(transform.position, target.position);
        if (distanceToTarget <= attackRange)
        {
            // Deal damage to target
            PlayerController playerController = target.GetComponent<PlayerController>();
            if (playerController != null)
            {
                playerController.TakeDamage(attackDamage);
                Debug.Log($"Enemy attacked player for {attackDamage} damage");
            }

            SquadMateAgent squadMate = target.GetComponent<SquadMateAgent>();
            if (squadMate != null)
            {
                squadMate.TakeDamage(attackDamage);
                Debug.Log($"Enemy attacked squadmate for {attackDamage} damage");
            }

            // Visual/audio feedback could go here
        }
    }

    public void TakeDamage(float damage)
    {
        if (isDead) return;

        currentHealth -= damage;
        currentHealth = Mathf.Max(currentHealth, 0);

        Debug.Log($"Enemy took {damage} damage. Health: {currentHealth}");

        // Become aggressive when attacked
        if (currentState == EnemyState.Patrolling)
        {
            currentState = EnemyState.Searching;
        }
    }

    private void Die()
    {
        isDead = true;
        currentState = EnemyState.Patrolling; // Reset state

        Debug.Log("Enemy died!");

        // Stop the NavMesh agent
        if (navAgent != null)
        {
            navAgent.enabled = false;
        }

        // Notify environment
        if (environment != null)
        {
            environment.RemoveEnemy(gameObject);
        }

        // Destroy after a delay
        Destroy(gameObject, 2f);
    }

    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);

        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);

        // Draw patrol area
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(patrolCenter, patrolRadius);

        // Draw current target
        if (target != null)
        {
            Gizmos.color = Color.magenta;
            Gizmos.DrawLine(transform.position, target.position);
        }

        // Draw patrol target
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(patrolTarget, 0.5f);

        // Draw health indicator
        Gizmos.color = Color.Lerp(Color.red, Color.green, currentHealth / maxHealth);
        Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, 0.3f);
    }

    void OnGUI()
    {
        // Debug info
        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position + Vector3.up * 3f);
        if (screenPos.z > 0)
        {
            GUI.Label(new Rect(screenPos.x - 50, Screen.height - screenPos.y, 100, 20),
                     $"HP: {currentHealth:F0}");
            GUI.Label(new Rect(screenPos.x - 50, Screen.height - screenPos.y + 20, 100, 20),
                     $"State: {currentState}");
        }
    }
}
