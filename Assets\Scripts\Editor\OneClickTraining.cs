using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.Diagnostics;
using System.IO;
using Unity.MLAgents.Policies;

/// <summary>
/// One-click training system for SquadMate AI
/// Sets up everything and starts ML-Agents training automatically
/// </summary>
public class OneClickTraining : EditorWindow
{
    [MenuItem("SquadMate AI/🚀 ONE CLICK TRAINING", priority = 0)]
    public static void ShowWindow()
    {
        OneClickTraining window = GetWindow<OneClickTraining>("One Click Training");
        window.minSize = new Vector2(450, 700);
        window.Show();
    }

    [MenuItem("SquadMate AI/🚀 INSTANT START TRAINING", priority = 1)]
    public static void InstantStartTraining()
    {
        if (EditorUtility.DisplayDialog("Instant Training",
            "This will:\n" +
            "• Create training scene\n" +
            "• Setup all components\n" +
            "• Start ML-Agents training\n" +
            "• Open TensorBoard\n\n" +
            "Continue?", "YES - START NOW!", "Cancel"))
        {
            StartOneClickTraining();
        }
    }

    private string runId = "squadmate_oneclick";
    private bool autoOpenTensorBoard = true;
    private bool useGPU = false;
    private int maxSteps = 500000;
    private bool debugMode = true;

    private static Process trainingProcess;
    private static Process tensorboardProcess;

    void OnGUI()
    {
        GUILayout.Label("🚀 SquadMate AI - One Click Training", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // Big prominent start button
        GUI.backgroundColor = Color.green;
        GUIStyle bigButtonStyle = new GUIStyle(GUI.skin.button);
        bigButtonStyle.fontSize = 16;
        bigButtonStyle.fontStyle = FontStyle.Bold;

        if (GUILayout.Button("🚀 START ONE CLICK TRAINING NOW!", bigButtonStyle, GUILayout.Height(60)))
        {
            StartOneClickTraining();
        }

        GUI.backgroundColor = Color.white;
        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This will automatically:\n" +
            "• Create training scene\n" +
            "• Setup all components\n" +
            "• Generate config files\n" +
            "• Start ML-Agents training\n" +
            "• Open TensorBoard (optional)", MessageType.Info);

        GUILayout.Space(10);

        // Training Settings
        GUILayout.Label("⚙️ Training Settings", EditorStyles.boldLabel);
        runId = EditorGUILayout.TextField("Run ID:", runId);
        maxSteps = EditorGUILayout.IntField("Max Steps:", maxSteps);
        useGPU = EditorGUILayout.Toggle("Use GPU:", useGPU);
        debugMode = EditorGUILayout.Toggle("Debug Mode:", debugMode);
        autoOpenTensorBoard = EditorGUILayout.Toggle("Auto Open TensorBoard:", autoOpenTensorBoard);

        GUILayout.Space(10);

        // Status
        GUILayout.Label("📊 Status", EditorStyles.boldLabel);

        if (trainingProcess != null && !trainingProcess.HasExited)
        {
            EditorGUILayout.HelpBox("✅ Training is running!", MessageType.Info);

            if (GUILayout.Button("🛑 Stop Training", GUILayout.Height(30)))
            {
                StopTraining();
            }
        }
        else
        {
            EditorGUILayout.HelpBox("⏸️ Training not running", MessageType.Warning);

            if (GUILayout.Button("🚀 START ONE CLICK TRAINING", GUILayout.Height(50)))
            {
                StartOneClickTraining();
            }
        }

        GUILayout.Space(10);

        // Quick Actions
        GUILayout.Label("🔧 Quick Actions", EditorStyles.boldLabel);

        if (GUILayout.Button("📊 Open TensorBoard"))
        {
            OpenTensorBoard();
        }

        if (GUILayout.Button("🎯 Create Scene Only"))
        {
            CreateTrainingScene();
        }

        if (GUILayout.Button("📁 Open Results Folder"))
        {
            OpenResultsFolder();
        }

        if (GUILayout.Button("🔄 Reset Everything"))
        {
            ResetEverything();
        }

        GUILayout.Space(10);

        // Help
        GUILayout.Label("❓ Help", EditorStyles.boldLabel);
        EditorGUILayout.HelpBox("If training doesn't start:\n" +
            "1. Install: pip install mlagents\n" +
            "2. Check Python/pip is in PATH\n" +
            "3. Try 'Reset Everything' first", MessageType.Info);
    }

    [MenuItem("SquadMate AI/🚀 START ONE CLICK TRAINING", priority = 1)]
    public static void StartOneClickTraining()
    {
        UnityEngine.Debug.Log("🚀 Starting One Click Training...");

        try
        {
            // Step 1: Create training scene
            CreateTrainingScene();

            // Step 2: Ensure config directory exists
            EnsureConfigDirectory();

            // Step 3: Create/update training config
            CreateTrainingConfig();

            // Step 4: Validate setup
            if (!ValidateSetup())
            {
                UnityEngine.Debug.LogError("❌ Setup validation failed!");
                return;
            }

            // Step 5: Start ML-Agents training
            StartMLAgentsTraining();

            // Step 6: Open TensorBoard (optional)
            if (HasWindow<OneClickTraining>())
            {
                OneClickTraining window = GetWindow<OneClickTraining>();
                if (window.autoOpenTensorBoard)
                {
                    EditorApplication.delayCall += () => OpenTensorBoard();
                }
            }
            else
            {
                EditorApplication.delayCall += () => OpenTensorBoard();
            }

            UnityEngine.Debug.Log("✅ One Click Training started successfully!");
            EditorUtility.DisplayDialog("Training Started",
                "SquadMate AI training has started!\n\n" +
                "• Check Console for progress\n" +
                "• TensorBoard will open automatically\n" +
                "• Training will run in background", "OK");
        }
        catch (System.Exception e)
        {
            UnityEngine.Debug.LogError($"❌ One Click Training failed: {e.Message}");
            EditorUtility.DisplayDialog("Training Failed",
                $"Failed to start training:\n{e.Message}\n\n" +
                "Check Console for details.", "OK");
        }
    }

    static void CreateTrainingScene()
    {
        UnityEngine.Debug.Log("🎯 Creating training scene...");

        // Create new scene
        var newScene = EditorSceneManager.NewScene(NewSceneSetup.DefaultGameObjects, NewSceneMode.Single);

        // Create the training arena
        CreateSimplePUBGScene.CreateSimpleScene();

        // Save the scene
        string scenePath = "Assets/Scenes/PUBGTrainingArena.unity";
        if (!Directory.Exists("Assets/Scenes"))
        {
            Directory.CreateDirectory("Assets/Scenes");
        }

        EditorSceneManager.SaveScene(newScene, scenePath);
        AssetDatabase.Refresh();

        UnityEngine.Debug.Log($"✅ Training scene created: {scenePath}");
    }

    static void EnsureConfigDirectory()
    {
        string configDir = Path.Combine(Application.dataPath, "..", "config");
        if (!Directory.Exists(configDir))
        {
            Directory.CreateDirectory(configDir);
            UnityEngine.Debug.Log($"📁 Created config directory: {configDir}");
        }
    }

    static void CreateTrainingConfig()
    {
        string configPath = Path.Combine(Application.dataPath, "..", "config", "squadmate-ppo.yaml");

        string configContent = @"behaviors:
  SquadMate:
    trainer_type: ppo
    hyperparameters:
      batch_size: 1024
      buffer_size: 10240
      learning_rate: 3.0e-4
      beta: 5.0e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3
      learning_rate_schedule: linear
    network_settings:
      normalize: false
      hidden_units: 128
      num_layers: 2
      vis_encode_type: simple
    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0
    max_steps: 500000
    time_horizon: 64
    summary_freq: 1000
    keep_checkpoints: 5
    checkpoint_interval: 50000
    threaded: false";

        File.WriteAllText(configPath, configContent);
        UnityEngine.Debug.Log($"📝 Created training config: {configPath}");
    }

    static bool ValidateSetup()
    {
        UnityEngine.Debug.Log("🔍 Validating setup...");

        // Check if scene has SquadMateAgent
        SquadMateAgent agent = FindObjectOfType<SquadMateAgent>();
        if (agent == null)
        {
            UnityEngine.Debug.LogError("❌ No SquadMateAgent found in scene!");
            return false;
        }

        // Check if agent has BehaviorParameters
        BehaviorParameters behaviorParams = agent.GetComponent<BehaviorParameters>();
        if (behaviorParams == null)
        {
            UnityEngine.Debug.LogError("❌ SquadMateAgent missing BehaviorParameters!");
            return false;
        }

        // Check behavior name
        if (behaviorParams.BehaviorName != "SquadMate")
        {
            behaviorParams.BehaviorName = "SquadMate";
            UnityEngine.Debug.Log("🔧 Fixed behavior name to 'SquadMate'");
        }

        // Check if config file exists
        string configPath = Path.Combine(Application.dataPath, "..", "config", "squadmate-ppo.yaml");
        if (!File.Exists(configPath))
        {
            UnityEngine.Debug.LogError($"❌ Config file not found: {configPath}");
            return false;
        }

        UnityEngine.Debug.Log("✅ Setup validation passed!");
        return true;
    }

    static void StartMLAgentsTraining()
    {
        UnityEngine.Debug.Log("🤖 Starting ML-Agents training...");

        string configPath = Path.Combine(Application.dataPath, "..", "config", "squadmate-ppo.yaml");
        string runId = "squadmate_oneclick_" + System.DateTime.Now.ToString("yyyyMMdd_HHmmss");

        // Build command
        string command = $"mlagents-learn \"{configPath}\" --run-id={runId} --force";

        // Add GPU support if available
        OneClickTraining window = HasWindow<OneClickTraining>() ? GetWindow<OneClickTraining>() : null;
        if (window != null && window.useGPU)
        {
            command += " --torch-device=cuda";
        }

        // Start process
        ProcessStartInfo startInfo = new ProcessStartInfo()
        {
            FileName = "cmd.exe",
            Arguments = $"/c {command}",
            UseShellExecute = false,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            CreateNoWindow = true,
            WorkingDirectory = Path.Combine(Application.dataPath, "..")
        };

        trainingProcess = new Process() { StartInfo = startInfo };

        trainingProcess.OutputDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                UnityEngine.Debug.Log($"[ML-Agents] {e.Data}");
            }
        };

        trainingProcess.ErrorDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                UnityEngine.Debug.LogWarning($"[ML-Agents] {e.Data}");
            }
        };

        trainingProcess.Start();
        trainingProcess.BeginOutputReadLine();
        trainingProcess.BeginErrorReadLine();

        UnityEngine.Debug.Log($"🚀 ML-Agents training started with run ID: {runId}");
        UnityEngine.Debug.Log("💡 Press Play in Unity to start training!");
    }

    static void OpenTensorBoard()
    {
        UnityEngine.Debug.Log("📊 Opening TensorBoard...");

        string resultsPath = Path.Combine(Application.dataPath, "..", "results");

        ProcessStartInfo startInfo = new ProcessStartInfo()
        {
            FileName = "cmd.exe",
            Arguments = $"/c tensorboard --logdir \"{resultsPath}\" --host localhost --port 6006",
            UseShellExecute = false,
            CreateNoWindow = true,
            WorkingDirectory = Path.Combine(Application.dataPath, "..")
        };

        tensorboardProcess = Process.Start(startInfo);

        // Open browser after a delay
        EditorApplication.delayCall += () =>
        {
            System.Threading.Thread.Sleep(3000);
            Application.OpenURL("http://localhost:6006");
        };

        UnityEngine.Debug.Log("📊 TensorBoard started at http://localhost:6006");
    }

    static void StopTraining()
    {
        UnityEngine.Debug.Log("🛑 Stopping training...");

        if (trainingProcess != null && !trainingProcess.HasExited)
        {
            trainingProcess.Kill();
            trainingProcess = null;
        }

        if (tensorboardProcess != null && !tensorboardProcess.HasExited)
        {
            tensorboardProcess.Kill();
            tensorboardProcess = null;
        }

        UnityEngine.Debug.Log("✅ Training stopped");
    }

    static void OpenResultsFolder()
    {
        string resultsPath = Path.Combine(Application.dataPath, "..", "results");
        if (Directory.Exists(resultsPath))
        {
            EditorUtility.RevealInFinder(resultsPath);
        }
        else
        {
            UnityEngine.Debug.LogWarning("Results folder not found. Start training first.");
        }
    }

    static void ResetEverything()
    {
        UnityEngine.Debug.Log("🔄 Resetting everything...");

        // Stop any running processes
        StopTraining();

        // Clear results
        string resultsPath = Path.Combine(Application.dataPath, "..", "results");
        if (Directory.Exists(resultsPath))
        {
            try
            {
                Directory.Delete(resultsPath, true);
                UnityEngine.Debug.Log("🗑️ Cleared results folder");
            }
            catch (System.Exception e)
            {
                UnityEngine.Debug.LogWarning($"Could not delete results: {e.Message}");
            }
        }

        UnityEngine.Debug.Log("✅ Reset complete");
    }

    void OnDestroy()
    {
        // Clean up processes when window is closed
        if (trainingProcess != null && !trainingProcess.HasExited)
        {
            trainingProcess.Kill();
        }

        if (tensorboardProcess != null && !tensorboardProcess.HasExited)
        {
            tensorboardProcess.Kill();
        }
    }
}
