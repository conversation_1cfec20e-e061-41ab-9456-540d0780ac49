{"count": 1, "self": 848.9737216, "total": 850.6070248, "children": {"InitializeActuators": {"count": 4, "self": 0.0019955999999999997, "total": 0.0019955999999999997, "children": null}, "InitializeSensors": {"count": 4, "self": 0.0009971, "total": 0.0009971, "children": null}, "AgentSendState": {"count": 338341, "self": 0.33150959999999996, "total": 0.33150959999999996, "children": null}, "DecideAction": {"count": 338341, "self": 1.1768614, "total": 1.1768614, "children": null}, "AgentAct": {"count": 338341, "self": 0.12094999999999999, "total": 0.12094999999999999, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}