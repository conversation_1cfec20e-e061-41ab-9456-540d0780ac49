using UnityEngine;
using Unity.MLAgents;

public class TrainingMonitor : MonoBehaviour
{
    [Header("📊 Training Statistics")]
    public float totalReward = 0f;
    public int episodeCount = 0;
    public float averageReward = 0f;
    public float bestReward = float.MinValue;
    public float worstReward = float.MaxValue;

    [Header("🎯 Performance Metrics")]
    public int successfulEpisodes = 0;
    public int failedEpisodes = 0;
    public float successRate = 0f;

    [Header("⏱️ Timing")]
    public float episodeStartTime;
    public float averageEpisodeLength = 0f;
    public float totalTrainingTime = 0f;

    private SquadMateAgent agent;
    private float trainingStartTime;

    void Start()
    {
        agent = GetComponent<SquadMateAgent>();
        trainingStartTime = Time.time;
        episodeStartTime = Time.time;

        if (agent != null)
        {
            Debug.Log("📊 Training Monitor initialized for SquadMate Agent");
        }
    }

    void Update()
    {
        totalTrainingTime = Time.time - trainingStartTime;

        // Update success rate
        if (episodeCount > 0)
        {
            successRate = (float)successfulEpisodes / episodeCount * 100f;
        }
    }

    public void OnEpisodeEnd(float episodeReward, bool wasSuccessful)
    {
        episodeCount++;
        totalReward += episodeReward;
        averageReward = totalReward / episodeCount;

        // Update best/worst rewards
        if (episodeReward > bestReward)
            bestReward = episodeReward;
        if (episodeReward < worstReward)
            worstReward = episodeReward;

        // Update success tracking
        if (wasSuccessful)
            successfulEpisodes++;
        else
            failedEpisodes++;

        // Update episode timing
        float episodeLength = Time.time - episodeStartTime;
        averageEpisodeLength = (averageEpisodeLength * (episodeCount - 1) + episodeLength) / episodeCount;
        episodeStartTime = Time.time;

        // Log progress every 10 episodes
        if (episodeCount % 10 == 0)
        {
            LogProgress();
        }
    }

    void LogProgress()
    {
        Debug.Log($"📊 TRAINING PROGRESS - Episode {episodeCount}");
        Debug.Log($"   💰 Avg Reward: {averageReward:F2} (Best: {bestReward:F2}, Worst: {worstReward:F2})");
        Debug.Log($"   🎯 Success Rate: {successRate:F1}% ({successfulEpisodes}/{episodeCount})");
        Debug.Log($"   ⏱️ Avg Episode: {averageEpisodeLength:F1}s, Total Time: {totalTrainingTime / 60:F1}min");
    }

    void OnGUI()
    {
        if (!Application.isPlaying) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.BeginVertical("box");

        GUILayout.Label("📊 SQUADMATE TRAINING");
        GUILayout.Space(5);

        GUILayout.Label($"Episodes: {episodeCount}");
        GUILayout.Label($"Success Rate: {successRate:F1}%");
        GUILayout.Label($"Avg Reward: {averageReward:F2}");
        GUILayout.Label($"Best Reward: {bestReward:F2}");
        GUILayout.Label($"Training Time: {totalTrainingTime / 60:F1}min");

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
