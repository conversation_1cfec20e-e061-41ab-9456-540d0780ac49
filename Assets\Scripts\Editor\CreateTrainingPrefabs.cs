using UnityEngine;
using UnityEditor;
using UnityEngine.AI;
using System.IO;

/// <summary>
/// Editor script to create training prefabs and fix the environment setup
/// </summary>
public class CreateTrainingPrefabs : EditorWindow
{
    [MenuItem("SquadMate/Fix Training Environment")]
    static void ShowWindow()
    {
        GetWindow<CreateTrainingPrefabs>("Fix Training Environment");
    }

    void OnGUI()
    {
        GUILayout.Label("Training Environment Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("1. Create Prefabs Directory"))
        {
            CreatePrefabsDirectory();
        }

        if (GUILayout.Button("2. Create Enemy Prefab"))
        {
            CreateEnemyPrefab();
        }

        if (GUILayout.Button("3. Create Medkit Prefab"))
        {
            CreateMedkitPrefab();
        }

        if (GUILayout.Button("4. Create Weapon Prefab"))
        {
            CreateWeaponPrefab();
        }

        if (GUILayout.Button("5. Assign Prefabs to Environment"))
        {
            AssignPrefabsToEnvironment();
        }

        if (GUILayout.Button("6. Initialize Environment"))
        {
            InitializeEnvironment();
        }

        GUILayout.Space(20);

        if (GUILayout.Button("🚀 FIX EVERYTHING", GUILayout.Height(40)))
        {
            FixEverything();
        }
    }

    static void CreatePrefabsDirectory()
    {
        string prefabsPath = "Assets/Prefabs";
        if (!AssetDatabase.IsValidFolder(prefabsPath))
        {
            AssetDatabase.CreateFolder("Assets", "Prefabs");
            Debug.Log("✅ Created Prefabs directory");
        }
        else
        {
            Debug.Log("📁 Prefabs directory already exists");
        }
    }

    static void CreateEnemyPrefab()
    {
        // Create enemy GameObject
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "Enemy";
        enemy.tag = "Enemy";

        // Add NavMesh Agent
        NavMeshAgent navAgent = enemy.AddComponent<NavMeshAgent>();
        navAgent.speed = 3f;
        navAgent.stoppingDistance = 2f;

        // Add EnemyController
        EnemyController enemyController = enemy.AddComponent<EnemyController>();

        // Set red material
        Renderer renderer = enemy.GetComponent<Renderer>();
        Material enemyMaterial = new Material(Shader.Find("Standard"));
        enemyMaterial.color = Color.red;
        renderer.sharedMaterial = enemyMaterial;

        // Save as prefab
        string prefabPath = "Assets/Prefabs/Enemy.prefab";
        PrefabUtility.SaveAsPrefabAsset(enemy, prefabPath);
        
        // Clean up scene
        DestroyImmediate(enemy);
        
        Debug.Log("✅ Created Enemy prefab");
    }

    static void CreateMedkitPrefab()
    {
        // Create medkit GameObject
        GameObject medkit = GameObject.CreatePrimitive(PrimitiveType.Cube);
        medkit.name = "Medkit";
        medkit.tag = "Medkit";

        // Make it a trigger
        Collider collider = medkit.GetComponent<Collider>();
        collider.isTrigger = true;

        // Add MedkitPickup script
        MedkitPickup medkitPickup = medkit.AddComponent<MedkitPickup>();

        // Set green material
        Renderer renderer = medkit.GetComponent<Renderer>();
        Material medkitMaterial = new Material(Shader.Find("Standard"));
        medkitMaterial.color = Color.green;
        renderer.sharedMaterial = medkitMaterial;

        // Scale it down a bit
        medkit.transform.localScale = Vector3.one * 0.5f;

        // Save as prefab
        string prefabPath = "Assets/Prefabs/Medkit.prefab";
        PrefabUtility.SaveAsPrefabAsset(medkit, prefabPath);
        
        // Clean up scene
        DestroyImmediate(medkit);
        
        Debug.Log("✅ Created Medkit prefab");
    }

    static void CreateWeaponPrefab()
    {
        // Create weapon GameObject
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        weapon.name = "Weapon";
        weapon.tag = "Weapon";

        // Make it a trigger
        Collider collider = weapon.GetComponent<Collider>();
        collider.isTrigger = true;

        // Add WeaponPickup script
        WeaponPickup weaponPickup = weapon.AddComponent<WeaponPickup>();

        // Set blue material
        Renderer renderer = weapon.GetComponent<Renderer>();
        Material weaponMaterial = new Material(Shader.Find("Standard"));
        weaponMaterial.color = Color.blue;
        renderer.sharedMaterial = weaponMaterial;

        // Scale and rotate it to look more like a weapon
        weapon.transform.localScale = new Vector3(0.3f, 1f, 0.3f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);

        // Save as prefab
        string prefabPath = "Assets/Prefabs/Weapon.prefab";
        PrefabUtility.SaveAsPrefabAsset(weapon, prefabPath);
        
        // Clean up scene
        DestroyImmediate(weapon);
        
        Debug.Log("✅ Created Weapon prefab");
    }

    static void AssignPrefabsToEnvironment()
    {
        // Find the Environment GameObject
        GameObject environmentObj = GameObject.Find("Environment");
        if (environmentObj == null)
        {
            Debug.LogError("❌ Environment GameObject not found!");
            return;
        }

        GameEnvironment gameEnv = environmentObj.GetComponent<GameEnvironment>();
        if (gameEnv == null)
        {
            Debug.LogError("❌ GameEnvironment component not found!");
            return;
        }

        // Load prefabs
        GameObject enemyPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Enemy.prefab");
        GameObject medkitPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Medkit.prefab");
        GameObject weaponPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Weapon.prefab");

        // Assign prefabs
        if (enemyPrefab != null)
        {
            gameEnv.enemyPrefab = enemyPrefab;
            Debug.Log("✅ Assigned Enemy prefab to Environment");
        }

        if (medkitPrefab != null)
        {
            gameEnv.medkitPrefab = medkitPrefab;
            Debug.Log("✅ Assigned Medkit prefab to Environment");
        }

        if (weaponPrefab != null)
        {
            gameEnv.weaponPrefab = weaponPrefab;
            Debug.Log("✅ Assigned Weapon prefab to Environment");
        }

        // Mark scene as dirty
        EditorUtility.SetDirty(gameEnv);
        
        Debug.Log("✅ All prefabs assigned to Environment");
    }

    static void InitializeEnvironment()
    {
        GameObject environmentObj = GameObject.Find("Environment");
        if (environmentObj == null)
        {
            Debug.LogError("❌ Environment GameObject not found!");
            return;
        }

        GameEnvironment gameEnv = environmentObj.GetComponent<GameEnvironment>();
        if (gameEnv == null)
        {
            Debug.LogError("❌ GameEnvironment component not found!");
            return;
        }

        // Initialize the environment
        gameEnv.InitializeEnvironment();
        
        Debug.Log("✅ Environment initialized with enemies, medkits, and weapons");
    }

    static void FixEverything()
    {
        Debug.Log("🚀 Starting complete training environment fix...");
        
        CreatePrefabsDirectory();
        CreateEnemyPrefab();
        CreateMedkitPrefab();
        CreateWeaponPrefab();
        AssignPrefabsToEnvironment();
        InitializeEnvironment();
        
        Debug.Log("🎉 Training environment fix completed!");
        Debug.Log("🌍 Your environment should now detect enemies, medkits, and weapons!");
    }
}
