using UnityEngine;
using UnityEditor;

/// <summary>
/// Editor script to create PUBG-style loot prefabs
/// </summary>
public class CreateLootPrefabs : EditorWindow
{
    [MenuItem("SquadMate/Create Loot Prefabs")]
    static void ShowWindow()
    {
        GetWindow<CreateLootPrefabs>("Create Loot Prefabs");
    }

    void OnGUI()
    {
        GUILayout.Label("🎒 PUBG Loot Prefab Creator", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("Weapons:", EditorStyles.boldLabel);
        if (GUILayout.Button("Create M416 Prefab"))
        {
            CreateM416Prefab();
        }
        if (GUILayout.Button("Create UMP45 Prefab"))
        {
            CreateUMP45Prefab();
        }
        if (GUILayout.Button("Create Kar98k Prefab"))
        {
            CreateKar98kPrefab();
        }

        GUILayout.Space(10);
        GUILayout.Label("Healing:", EditorStyles.boldLabel);
        if (GUILayout.Button("Create MedKit Prefab"))
        {
            CreateMedKitPrefab();
        }
        if (GUILayout.Button("Create FirstAid Prefab"))
        {
            CreateFirstAidPrefab();
        }
        if (GUILayout.Button("Create EnergyDrink Prefab"))
        {
            CreateEnergyDrinkPrefab();
        }

        GUILayout.Space(10);
        GUILayout.Label("Utilities:", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Smoke Grenade Prefab"))
        {
            CreateSmokeGrenadePrefab();
        }
        if (GUILayout.Button("Create Frag Grenade Prefab"))
        {
            CreateFragGrenadePrefab();
        }

        GUILayout.Space(20);
        if (GUILayout.Button("🚀 CREATE ALL LOOT PREFABS", GUILayout.Height(40)))
        {
            CreateAllLootPrefabs();
        }
    }

    static void CreateM416Prefab()
    {
        GameObject weapon = CreateBaseLootObject("M416", PrimitiveType.Cylinder);

        // Add existing WeaponPickup component
        WeaponPickup weaponPickup = weapon.AddComponent<WeaponPickup>();
        weaponPickup.weaponType = "M416";
        weaponPickup.damage = 41f;
        weaponPickup.range = 25f;
        weaponPickup.fireRate = 0.086f;
        weaponPickup.ammo = 30;

        // Set visual properties
        weapon.transform.localScale = new Vector3(0.3f, 1.2f, 0.3f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);

        // Set blue color for AR
        Renderer renderer = weapon.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.blue;

        SavePrefab(weapon, "M416");
    }

    static void CreateUMP45Prefab()
    {
        GameObject weapon = CreateBaseLootObject("UMP45", PrimitiveType.Cylinder);

        WeaponPickup weaponPickup = weapon.AddComponent<WeaponPickup>();
        weaponPickup.weaponType = "UMP45";
        weaponPickup.damage = 35f;
        weaponPickup.range = 15f;
        weaponPickup.fireRate = 0.092f;
        weaponPickup.ammo = 25;

        weapon.transform.localScale = new Vector3(0.25f, 0.8f, 0.25f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);

        // Set cyan color for SMG
        Renderer renderer = weapon.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.cyan;

        SavePrefab(weapon, "UMP45");
    }

    static void CreateKar98kPrefab()
    {
        GameObject weapon = CreateBaseLootObject("Kar98k", PrimitiveType.Cylinder);

        WeaponPickup weaponPickup = weapon.AddComponent<WeaponPickup>();
        weaponPickup.weaponType = "Kar98k";
        weaponPickup.damage = 75f;
        weaponPickup.range = 50f;
        weaponPickup.fireRate = 1.8f;
        weaponPickup.ammo = 5;

        weapon.transform.localScale = new Vector3(0.2f, 1.5f, 0.2f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);

        // Set magenta color for sniper
        Renderer renderer = weapon.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.magenta;

        SavePrefab(weapon, "Kar98k");
    }

    static void CreateMedKitPrefab()
    {
        GameObject healing = CreateBaseLootObject("MedKit", PrimitiveType.Cube);

        // Add existing MedkitPickup component
        MedkitPickup medkitPickup = healing.AddComponent<MedkitPickup>();
        medkitPickup.healAmount = 100f;
        medkitPickup.instantHeal = true;

        healing.transform.localScale = new Vector3(0.6f, 0.3f, 0.8f);

        // Set cyan color for MedKit
        Renderer renderer = healing.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.cyan;

        SavePrefab(healing, "MedKit");
    }

    static void CreateFirstAidPrefab()
    {
        GameObject healing = CreateBaseLootObject("FirstAidKit", PrimitiveType.Cube);

        MedkitPickup medkitPickup = healing.AddComponent<MedkitPickup>();
        medkitPickup.healAmount = 75f;
        medkitPickup.instantHeal = true;

        healing.transform.localScale = new Vector3(0.5f, 0.2f, 0.7f);

        // Set green color for FirstAid
        Renderer renderer = healing.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.green;

        SavePrefab(healing, "FirstAidKit");
    }

    static void CreateEnergyDrinkPrefab()
    {
        GameObject healing = CreateBaseLootObject("EnergyDrink", PrimitiveType.Cylinder);

        MedkitPickup medkitPickup = healing.AddComponent<MedkitPickup>();
        medkitPickup.healAmount = 40f;
        medkitPickup.instantHeal = true;

        healing.transform.localScale = new Vector3(0.3f, 0.6f, 0.3f);

        // Set yellow color for EnergyDrink
        Renderer renderer = healing.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.yellow;

        SavePrefab(healing, "EnergyDrink");
    }

    static void CreateSmokeGrenadePrefab()
    {
        GameObject utility = CreateBaseLootObject("SmokeGrenade", PrimitiveType.Sphere);

        // Use WeaponPickup for grenades (they're tactical items)
        WeaponPickup weaponPickup = utility.AddComponent<WeaponPickup>();
        weaponPickup.weaponType = "SmokeGrenade";
        weaponPickup.damage = 0f; // No damage
        weaponPickup.range = 20f; // Throw range
        weaponPickup.fireRate = 2f; // Cooldown
        weaponPickup.ammo = 3; // 3 uses

        utility.transform.localScale = Vector3.one * 0.4f;

        // Set gray color for smoke
        Renderer renderer = utility.GetComponent<Renderer>();
        renderer.sharedMaterial.color = Color.gray;

        SavePrefab(utility, "SmokeGrenade");
    }

    static void CreateFragGrenadePrefab()
    {
        GameObject utility = CreateBaseLootObject("FragGrenade", PrimitiveType.Sphere);

        WeaponPickup weaponPickup = utility.AddComponent<WeaponPickup>();
        weaponPickup.weaponType = "FragGrenade";
        weaponPickup.damage = 100f; // High damage
        weaponPickup.range = 25f; // Throw range
        weaponPickup.fireRate = 3f; // Cooldown
        weaponPickup.ammo = 2; // 2 uses

        utility.transform.localScale = Vector3.one * 0.35f;

        // Set dark green color for frag
        Renderer renderer = utility.GetComponent<Renderer>();
        renderer.sharedMaterial.color = new Color(0.2f, 0.4f, 0.2f);

        SavePrefab(utility, "FragGrenade");
    }

    static GameObject CreateBaseLootObject(string name, PrimitiveType primitiveType)
    {
        GameObject obj = GameObject.CreatePrimitive(primitiveType);
        obj.name = name;

        // Set appropriate tag based on item type
        if (name.Contains("Grenade"))
        {
            obj.tag = "Weapon"; // Grenades are weapons
        }
        else if (name.Contains("Med") || name.Contains("Aid") || name.Contains("Energy"))
        {
            obj.tag = "Medkit"; // Healing items
        }
        else
        {
            obj.tag = "Weapon"; // Default for weapons
        }

        // Make collider a trigger
        Collider collider = obj.GetComponent<Collider>();
        if (collider != null)
        {
            collider.isTrigger = true;
        }

        // Create material
        Renderer renderer = obj.GetComponent<Renderer>();
        Material material = new Material(Shader.Find("Standard"));
        renderer.sharedMaterial = material;

        return obj;
    }

    static void SavePrefab(GameObject obj, string prefabName)
    {
        // Ensure Prefabs/Loot directory exists
        string lootPath = "Assets/Prefabs/Loot";
        if (!AssetDatabase.IsValidFolder(lootPath))
        {
            if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
            {
                AssetDatabase.CreateFolder("Assets", "Prefabs");
            }
            AssetDatabase.CreateFolder("Assets/Prefabs", "Loot");
        }

        // Save prefab
        string prefabPath = $"{lootPath}/{prefabName}.prefab";
        PrefabUtility.SaveAsPrefabAsset(obj, prefabPath);

        // Clean up scene
        DestroyImmediate(obj);

        Debug.Log($"✅ Created {prefabName} prefab at {prefabPath}");
    }

    public static void CreateAllLootPrefabs()
    {
        Debug.Log("🚀 Creating all PUBG loot prefabs...");

        // Weapons
        CreateM416Prefab();
        CreateUMP45Prefab();
        CreateKar98kPrefab();

        // Healing
        CreateMedKitPrefab();
        CreateFirstAidPrefab();
        CreateEnergyDrinkPrefab();

        // Utilities
        CreateSmokeGrenadePrefab();
        CreateFragGrenadePrefab();

        Debug.Log("🎉 All PUBG loot prefabs created successfully!");
        Debug.Log("📁 Check Assets/Prefabs/Loot/ for your new prefabs");

        // Refresh asset database
        AssetDatabase.Refresh();
    }
}
